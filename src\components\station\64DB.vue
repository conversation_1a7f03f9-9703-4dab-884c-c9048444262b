<template>
  <g v-for="item in data" :key="item.usIndex+'64DB'">
    <g v-for="(arrow, arrowIndex) in item.arrowConfig" :key="'arrow'+arrowIndex">
      <polygon :class="arrowStyle" :points="getArrowPoints(arrow)" />
    </g>
    <g v-for="text in item.textConfig" :key="text.usIndex">
      <text :class="textStyle" :x="text.x" :y="text.y">{{ text.text }}</text>
    </g>
  </g>
</template>

<script setup>

const props = defineProps({
  data: {
    type: Array,
    required: true
  },
  theme: {
    type: Object,
    required: true
  },
})

const { arrowStyle, textStyle, redTextStyle } = props.theme 

// 使用 Map 缓存箭头点计算结果
const arrowPointsCache = new Map()

const getArrowPoints = (arrow) => {
  const { x, y, dir } = arrow;
  
  // 创建缓存键
  const cacheKey = `${x}-${y}-${dir}`
  
  // 检查缓存
  if (arrowPointsCache.has(cacheKey)) {
    return arrowPointsCache.get(cacheKey)
  }
  
  const points = [];
  // 1左，2右
  if(dir === 1){
    points.push(`${x},${y-3}`)
    points.push(`${x-12},${y-3}`)
    points.push(`${x-12},${y-7}`)
    points.push(`${x-19},${y}`)
    points.push(`${x-12},${y+7}`)
    points.push(`${x-12},${y+3}`)
    points.push(`${x},${y+3}`)
  } else if(dir === 2){
    points.push(`${x},${y-3}`)
    points.push(`${x+12},${y-3}`)
    points.push(`${x+12},${y-7}`)
    points.push(`${x+19},${y}`)
    points.push(`${x+12},${y+7}`)
    points.push(`${x+12},${y+3}`)
    points.push(`${x},${y+3}`)
  }
  
  const result = points.join(' ');
  // 缓存结果
  arrowPointsCache.set(cacheKey, result)
  
  return result;
}
</script>

<style scoped>   
</style>