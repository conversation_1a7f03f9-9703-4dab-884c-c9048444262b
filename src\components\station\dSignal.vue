<template>
  <!-- 调车信号机 -->
  <svg attr="调车信号机">
    <g v-for="item in processedData" :key="item.usIndex">
      <line v-for="(line,index) in item.lineConfig" :key="'line'+index" :class="lineStyle" :x1="line.x1" :y1="line.y1" :x2="line.x2" :y2="line.y2" />
      <text :class="textStyle" :x="item.textX" :y="item.textY"> {{ item.text }} </text>
      <text :class="redTextStyle" :x="item.redTextX" :y="item.redTextY"> {{ item.redText }} </text>
      <!-- 默认灯位闪烁，动态数据中如果没有包括闪烁字段，则不用闪烁；如果包括闪烁字段，则闪烁；如果推送了stroke，则stroke用推送的，如果没有推送，则stroke用fill的 -->
      <circle :class="[circleStyle, Object.keys(getCircleStyle(item)).length > 0 ? '' : 'flash-active']" :style="getCircleStyle(item)" :cx="item.circleX" :cy="item.circleY" />
    </g>
  </svg>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  theme: {
    type: Object,
    required: true
  },
  data: {
    type: Array,
    required: true
  },
})

const { lineStyle, textStyle, redTextStyle, circleStyle } = props.theme

// 使用 computed 缓存处理后的数据，避免重复计算
const processedData = computed(() => {
  return props.data.map(item => {
    const textConfig = item.textConfig?.[0]
    const redTextConfig = item.redTextConfig?.[0]
    const circleConfig = item.sigCircleConfig?.[0]
    
    return {
      ...item, // 保留原始数据
      text: textConfig?.text || '',
      textX: textConfig?.x || 0,
      textY: textConfig?.y || 0,
      redText: redTextConfig?.text || '',
      redTextX: redTextConfig?.x || 0,
      redTextY: redTextConfig?.y || 0,
      circleX: circleConfig?.x || 0,
      circleY: circleConfig?.y || 0,
      dynamicStyle: {
        circleCClrFill: circleConfig?.cClrFill || '',
        circleCClrStroke: circleConfig?.cClrStroke || ''
      }
    }
  })
})

const getCircleStyle = (item) => {
  const styles = {}
  if (item.dynamicStyle.circleCClrFill) {
    styles.fill = `rgb(${item.dynamicStyle.circleCClrFill})`
    styles.stroke = `rgb(${item.dynamicStyle.circleCClrFill})`
  }
  if (item.dynamicStyle.circleCClrStroke) {
    styles.stroke = `rgb(${item.dynamicStyle.circleCClrStroke})`
  }
  return styles 
}

</script>

<style lang="scss" scoped>

</style>