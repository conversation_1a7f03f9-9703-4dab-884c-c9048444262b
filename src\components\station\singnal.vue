<template>
  <svg attr="信号机">
    <g v-for="item in processedData" :key="item.usIndex">
      <line v-for="(line, index) in item.lineConfig" :key="'line'+index" :class="lineStyle" :x1="line.x1" :y1="line.y1" :x2="line.x2"
        :y2="line.y2" />
      <text :class="textStyle" v-if="item.textX>0" :x="item.textX" :y="item.textY"> {{ item.text }} </text>
      <text :class="redTextStyle" v-if="item.redTextX>0" :x="item.redTextX" :y="item.redTextY"> {{ item.redText }} </text>
      <circle :class="[index==0?circleStyle:circleStyle2, 'flash-active']" v-for="(circle, index) in item.circleObj" :key="index" :style="getCircleStyle(circle)" :cx="circle.x" :cy="circle.y" />
      <g v-for="(line, index) in item.crossingConfig" v-if="item.bCrossing" :key="'crossing'+index">
        <line :class="crossingStyle" :x1="line.x1" :y1="line.y1" :x2="line.x2" :y2="line.y2" />
        <line :class="crossingStyle" :x1="line.x3" :y1="line.y3" :x2="line.x4" :y2="line.y4" />
      </g>
    </g>
  </svg>
</template>

<script setup>
import { computed } from 'vue'

// 安全侧时，第一个灯红闪，第二个灯空圈
const props = defineProps({
  data: {
    type: Array,
    required: true
  },
  theme: {
    type: Object,
    required: true
  },
})

const { lineStyle, textStyle, redTextStyle, circleStyle, circleStyle2, crossingStyle } = props.theme

// 使用 computed 缓存处理后的数据，避免重复计算
const processedData = computed(() => {
  return props.data.map(item => {
    const textObj = item.textConfig?.[0]
    const redTextObj = item.redTextConfig?.[0]
    const circleObj = item.sigCircleConfig || []
    
    return {
      ...item, // 保留原始数据
      text: textObj?.text,
      textX: textObj?.x,
      textY: textObj?.y,
      circleObj,
      redText: redTextObj?.text,
      redTextX: redTextObj?.x,
      redTextY: redTextObj?.y
    }
  })
})

const getCircleStyle = (element) => {
  return {
    fill: element.cClrFill?`rgb(${element.cClrFill})`:'',
    stroke: element.cClrStroke?`rgb(${element.cClrStroke})`:element.cClrFill?`rgb(${element.cClrFill})`:''
  }
}

</script>

<style lang="scss" scoped>

</style>