<template>
  <div class="replay-tool">
    <div class="replay-tool-container">
      <el-select v-model="replayTarget" style="width:260px;" class="m-2" placeholder="Select">
        <el-option
          v-for="item in replayTargetOptions"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>

      <el-button
        :type="isPaused ? 'warning' : 'success'"
        @click="togglePlay"
        class="m-2"
      >
        {{ isPaused ? "开始" : "暂停" }}
      </el-button>

      <div class="time-slider-container">
        <span class="time-display">{{ currentTime }}</span>
        <el-slider v-model="progress" class="replay-slider" />
      </div>

      <el-button class="m-2" @click="stepForward">单步向前</el-button>
      <el-button class="m-2" @click="stepBackward">单步向后</el-button>
      <el-button class="m-2" @click="setContinuous">连续</el-button>
      <el-button class="m-2" @click="endPlayback">结束</el-button>

      <div class="spacer"></div>

      <span class="speed-label">速度</span>
      <el-select v-model="speed" style="width:160px;" class="m-2 speed-select" placeholder="Select">
        <el-option
          v-for="item in speedOptions"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>

      <el-button class="m-2" @click="resetTime">重设时间</el-button>
    </div>
  </div>
</template>

<script setup>
import { computed, onMounted, onUnmounted } from "vue"
import { useStore } from 'vuex'

const store = useStore()

// 从Vuex获取状态
const replayTarget = computed({
  get: () => store.state.historyPlayback.replayTarget,
  set: (value) => store.dispatch('historyPlayback/setReplayTarget', value)
})

const replayTargetOptions = computed(() => store.state.historyPlayback.replayTargetOptions)

const isPaused = computed(() => store.state.historyPlayback.isPaused)

const currentTime = computed(() => store.getters['historyPlayback/formattedCurrentTime'])

const progress = computed({
  get: () => store.state.historyPlayback.progress,
  set: (value) => store.dispatch('historyPlayback/updateProgress', value)
})

const speed = computed({
  get: () => store.state.historyPlayback.speed,
  set: (value) => store.dispatch('historyPlayback/setSpeed', value)
})

const speedOptions = computed(() => store.state.historyPlayback.speedOptions)

// 方法
const togglePlay = () => {
  store.dispatch('historyPlayback/togglePlay')
  console.log('播放状态切换:', isPaused.value ? '已暂停' : '正在播放')
}

const stepForward = () => {
  console.log('单步向前')
  // 这里可以添加单步向前的逻辑
}

const stepBackward = () => {
  console.log('单步向后')
  // 这里可以添加单步向后的逻辑
}

const setContinuous = () => {
  console.log('设置连续播放')
  // 这里可以添加连续播放的逻辑
}

const endPlayback = () => {
  console.log('结束播放')
  store.dispatch('historyPlayback/resetPlayback')
}

const resetTime = () => {
  console.log('重设时间')
  const now = new Date()
  const timeString = now.toISOString().replace('T', ' ').slice(0, 23)
  store.dispatch('historyPlayback/updateCurrentTime', timeString)
}

// 模拟播放进度更新
let playbackTimer = null

const startPlaybackTimer = () => {
  if (!isPaused.value && !playbackTimer) {
    playbackTimer = setInterval(() => {
      if (!isPaused.value) {
        const currentProgress = store.state.historyPlayback.progress
        const speedMultiplier = store.state.historyPlayback.speed
        const increment = 0.1 * speedMultiplier // 根据速度调整进度增量
        
        const newProgress = Math.min(currentProgress + increment, 100)
        store.dispatch('historyPlayback/updateProgress', newProgress)
        
        // 同时更新时间显示
        const now = new Date()
        const timeString = `${now.getFullYear()}/${String(now.getMonth() + 1).padStart(2, '0')}/${String(now.getDate()).padStart(2, '0')} ${String(now.getHours()).padStart(2, '0')}:${String(now.getMinutes()).padStart(2, '0')}:${String(now.getSeconds()).padStart(2, '0')}.${String(now.getMilliseconds()).padStart(3, '0')}`
        store.dispatch('historyPlayback/updateCurrentTime', timeString)
        
        // 如果播放完成，自动暂停
        if (newProgress >= 100) {
          store.dispatch('historyPlayback/togglePlay')
        }
      }
    }, 100) // 每100ms更新一次
  }
}

const stopPlaybackTimer = () => {
  if (playbackTimer) {
    clearInterval(playbackTimer)
    playbackTimer = null
  }
}

// 监听播放状态变化
const watchPlaybackState = () => {
  if (!isPaused.value) {
    startPlaybackTimer()
  } else {
    stopPlaybackTimer()
  }
}

// 生命周期
onMounted(() => {
  // 恢复状态
  store.dispatch('historyPlayback/restoreFromStorage')
  
  // 监听播放状态
  store.watch(
    () => store.state.historyPlayback.isPaused,
    () => watchPlaybackState()
  )
  
  console.log('ReplayTool组件已挂载，状态已恢复')
})

onUnmounted(() => {
  stopPlaybackTimer()
})
</script>

<style lang="scss" scoped>
.replay-tool {
  width: 100%;
  height: 60px;
  position: fixed;
  left: 0;
  bottom: 0;
  background-color: rgb(255, 140, 0);
  display: flex;
  align-items: center;
  padding: 0 20px;
  box-sizing: border-box;
  font-family: "SimSun", "宋体", sans-serif;
}

.replay-tool-container {
  display: flex;
  align-items: center;
  width: 100%;
}

.time-slider-container {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin: 0 15px;
}

.time-display {
  color: #0f0;
  font-family: monospace;
  font-weight: bold;
}

.replay-slider {
  width: 100%;
  --el-slider-main-bg-color: #a9a9a9;
  --el-slider-runway-bg-color: #a9a9a9;
}

.spacer {
  flex-grow: 1;
}

.speed-label {
  margin-left: 20px;
  width: 80px;
  margin-right: 10px;
  color: #000;
}

.speed-select {
  width: 80px;
}

.m-2 {
  margin: 0 5px !important;
}

:deep(.el-slider__button) {
  width: 10px;
  height: 20px;
  border-radius: 0;
  background-color: #c0c0c0;
  border: 1px solid;
  border-color: #fff #888 #888 #fff;
}

:deep(.el-button) {
  background-color: #fcae3e;
  border: 1px solid;
  border-color: #fff #9d641c #9d641c #fff;
  color: black;
  font-weight: bold;
}

:deep(.el-button:active) {
  border-color: #9d641c #fff #fff #9d641c;
}

:deep(.el-button--success) {
  background-color: #52b146 !important;
  border-color: #fff #37752e #37752e #fff !important;
}

:deep(.el-button--success:active) {
  border-color: #37752e #fff #fff #37752e !important;
}

:deep(.el-button--warning) {
  background-color: #fcae3e !important;
  border-color: #fff #9d641c #9d641c #fff !important;
}

:deep(.el-button--warning:active) {
  border-color: #9d641c #fff #fff #9d641c !important;
}

:deep(.el-select .el-input__wrapper) {
  background-color: white;
  box-shadow: 0 0 0 1px #888 inset !important;
  border: 1px solid;
  border-color: #fff #888 #888 #fff;
}

:deep(.el-select .el-input__inner) {
  color: black;
  font-weight: bold;
}

:deep(.el-select .el-input .el-select__caret) {
  color: black;
}
</style>

<style lang="scss">
.el-select__popper {
  border: 1px solid black !important;
  background-color: #fcae3e !important;

  .el-select-dropdown__item {
    color: black;
    font-family: "SimSun", "宋体", sans-serif;
  }
  .el-select-dropdown__item.is-selected {
    background-color: #0000a0 !important;
    color: white;
  }
  .el-select-dropdown__item.hover,
  .el-select-dropdown__item:hover {
    background-color: #ffc97e;
  }

  .el-popper__arrow::before {
    background: #fcae3e !important;
    border-color: black !important;
    border-width: 1px 0 0 1px !important;
  }
}
</style>