{"name": "hollicbi-b", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@vue/compat": "^3.5.16", "axios": "^1.10.0", "element-plus": "^2.10.2", "moment": "^2.30.1", "sass-embedded": "^1.89.2", "svg-pan-zoom": "^3.6.2", "vue": "^3.5.13", "vue-imask": "^7.6.1", "vue-router": "^4.5.0", "vue-svg-pan-zoom": "^2.1.0", "vuex": "^4.0.2"}, "devDependencies": {"@vitejs/plugin-vue": "^5.2.3", "vite": "^6.2.4", "vite-plugin-vue-devtools": "^7.7.2"}}