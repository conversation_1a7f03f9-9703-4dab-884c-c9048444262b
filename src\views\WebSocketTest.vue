<template>
  <div class="websocket-test">
    <h2>WebSocket系统测试页面</h2>
    
    <div class="test-section">
      <h3>连接状态</h3>
      <p>WebSocket连接状态: <span :class="connectionClass">{{ connectionText }}</span></p>
      <p>当前订阅Topic: <span class="topic">{{ currentTopic || '无' }}</span></p>
      <p>重连信息: {{ reconnectInfo.isReconnecting ? `重连中 ${reconnectInfo.count}/${reconnectInfo.maxCount}` : '正常' }}</p>
    </div>

    <div class="test-section">
      <h3>操作测试</h3>
      <div class="button-group">
        <button @click="testConnect" :disabled="isConnected">连接WebSocket</button>
        <button @click="testDisconnect" :disabled="!isConnected">断开连接</button>
        <button @click="testSubscribe">订阅测试Topic</button>
        <button @click="testQuery">查询数据</button>
        <button @click="clearData">清除数据</button>
      </div>
    </div>

    <div class="test-section">
      <h3>接收到的数据</h3>
      <div class="data-display">
        <p>最后更新时间: {{ lastUpdateTime || '无数据' }}</p>
        <pre v-if="websocketData">{{ JSON.stringify(websocketData, null, 2) }}</pre>
        <p v-else class="no-data">暂无数据</p>
      </div>
    </div>

    <div class="test-section">
      <h3>所有消息数据</h3>
      <div class="all-data">
        <pre v-if="allMessageData && Object.keys(allMessageData).length > 0">{{ JSON.stringify(allMessageData, null, 2) }}</pre>
        <p v-else class="no-data">暂无消息数据</p>
      </div>
    </div>

    <div class="test-section">
      <h3>错误信息</h3>
      <div class="error-display">
        <p v-if="error" class="error">{{ error }}</p>
        <p v-else class="no-error">无错误</p>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, inject, onMounted } from 'vue'
import { useStore } from 'vuex'

const store = useStore()

// 注入App.vue提供的数据和方法
const websocketData = inject('websocketData')
const queryData = inject('queryData')

// 本地状态
const lastUpdateTime = ref(null)

// 计算属性
const isConnected = computed(() => store.getters['websocket/isConnected'])
const currentTopic = computed(() => store.getters['websocket/currentTopic'])
const reconnectInfo = computed(() => store.getters['websocket/getReconnectInfo'])
const error = computed(() => store.getters['websocket/getError'])
const allMessageData = computed(() => store.getters['websocket/getAllMessageData'])

const connectionClass = computed(() => ({
  'connected': isConnected.value,
  'disconnected': !isConnected.value
}))

const connectionText = computed(() => {
  return isConnected.value ? '已连接' : '已断开'
})

// 监听WebSocket数据变化
watch(websocketData, (newData) => {
  if (newData) {
    lastUpdateTime.value = new Date().toLocaleString()
    console.log('测试页面收到WebSocket数据:', newData)
  }
}, { deep: true })

// 测试方法
const testConnect = async () => {
  try {
    await store.dispatch('websocket/initWebSocket')
    console.log('手动连接WebSocket成功')
  } catch (error) {
    console.error('手动连接WebSocket失败:', error)
  }
}

const testDisconnect = () => {
  store.dispatch('websocket/closeWebSocket')
  console.log('手动断开WebSocket连接')
}

const testSubscribe = async () => {
  try {
    await store.dispatch('websocket/subscribeTopic', { 
      topic: 'test/topic',
      params: { test: true }
    })
    console.log('订阅测试topic成功')
  } catch (error) {
    console.error('订阅测试topic失败:', error)
  }
}

const testQuery = async () => {
  if (queryData) {
    try {
      await queryData('test/query', {
        action: 'test',
        timestamp: Date.now()
      })
      console.log('查询测试数据请求已发送')
    } catch (error) {
      console.error('查询测试数据失败:', error)
    }
  }
}

const clearData = () => {
  lastUpdateTime.value = null
  store.commit('websocket/CLEAR_MESSAGE_DATA')
  console.log('清除所有数据')
}

onMounted(() => {
  console.log('WebSocket测试页面已挂载')
})
</script>

<style scoped>
.websocket-test {
  padding: 20px;
  max-width: 1000px;
  margin: 0 auto;
  font-family: Arial, sans-serif;
}

h2 {
  color: #333;
  border-bottom: 2px solid #409eff;
  padding-bottom: 10px;
  margin-bottom: 30px;
}

h3 {
  color: #666;
  margin-bottom: 15px;
}

.test-section {
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  background: #fafafa;
}

.connected {
  color: #67c23a;
  font-weight: bold;
}

.disconnected {
  color: #f56c6c;
  font-weight: bold;
}

.topic {
  color: #409eff;
  font-weight: bold;
}

.button-group {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

button {
  padding: 8px 16px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  background: #fff;
  cursor: pointer;
  transition: all 0.2s;
}

button:hover:not(:disabled) {
  background: #ecf5ff;
  border-color: #b3d8ff;
  color: #409eff;
}

button:disabled {
  background: #f5f7fa;
  border-color: #e4e7ed;
  color: #c0c4cc;
  cursor: not-allowed;
}

.data-display, .all-data, .error-display {
  background: #fff;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 15px;
  max-height: 300px;
  overflow-y: auto;
}

pre {
  margin: 0;
  font-size: 12px;
  white-space: pre-wrap;
  word-break: break-all;
}

.no-data, .no-error {
  color: #909399;
  font-style: italic;
}

.error {
  color: #f56c6c;
  font-weight: bold;
}
</style>
