import axios from 'axios'

// 创建axios实例
const service = axios.create({
  baseURL: sessionStorage.getItem('baseURL'), // 根据环境设置baseURL
  timeout: 10000, // 请求超时时间
  headers: {
    'Content-Type': 'application/json;charset=UTF-8'
  }
})

// 请求拦截器
service.interceptors.request.use(
  config => {
    // 在发送请求之前做些什么
    // 可以在这里添加token
    const token = localStorage.getItem('token') || sessionStorage.getItem('token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    config.headers['Access-Control-Allow-Origin'] = "*";
    config.headers['Access-Control-Allow-Methods'] = "POST";
    config.headers['Access-Control-Allow-Headers'] = "Access-Control";
    config.headers['Allow'] = "POST";
    
    // 显示loading
    // 可以在这里添加loading效果
    
    return config
  },
  error => {
    // 对请求错误做些什么
    console.error('请求错误:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
service.interceptors.response.use(
  response => {
    // 隐藏loading
    
    // 对响应数据做点什么
    const res = response.data
    
    // 根据后端返回的状态码进行判断
    if (res.code !== undefined) {
      // 如果返回的状态码为200，说明接口请求成功，可以正常拿到数据
      if (res.code === 200 || res.code === 0) {
        return res
      } else {
        // 如果不是正常状态，根据后端约定进行处理
        console.error('接口返回错误:', res.message || '未知错误')
        
        // token失效，跳转登录页
        if (res.code === 401 || res.code === 403) {
          localStorage.removeItem('token')
          sessionStorage.removeItem('token')
          // 这里可以添加路由跳转到登录页的逻辑
          // router.push('/login')
        }
        
        return Promise.reject(new Error(res.message || 'Error'))
      }
    } else {
      // 如果后端没有返回code字段，直接返回data
      return response
    }
  },
  error => {
    // 隐藏loading
    
    console.error('响应错误:', error)
    
    // 对响应错误做点什么
    let message = '网络错误'
    
    if (error.response) {
      switch (error.response.status) {
        case 400:
          message = '请求错误(400)'
          break
        case 401:
          message = '未授权，请重新登录(401)'
          localStorage.removeItem('token')
          sessionStorage.removeItem('token')
          // router.push('/login')
          break
        case 403:
          message = '拒绝访问(403)'
          break
        case 404:
          message = '请求出错(404)'
          break
        case 408:
          message = '请求超时(408)'
          break
        case 500:
          message = '服务器错误(500)'
          break
        case 501:
          message = '服务未实现(501)'
          break
        case 502:
          message = '网络错误(502)'
          break
        case 503:
          message = '服务不可用(503)'
          break
        case 504:
          message = '网络超时(504)'
          break
        case 505:
          message = 'HTTP版本不受支持(505)'
          break
        default:
          message = `连接出错(${error.response.status})!`
      }
    } else {
      message = '连接服务器失败!'
    }
    
    // 这里可以添加全局错误提示
    // Message.error(message)
    
    return Promise.reject(error)
  }
)

// 封装常用的HTTP方法
const http = {
  // GET请求
  get(url, params = {}) {
    return service({
      method: 'get',
      url,
      params
    })
  },
  
  // POST请求
  post(url, data = {}) {
    return service({
      method: 'post',
      url,
      data
    })
  },
  
  // PUT请求
  put(url, data = {}) {
    return service({
      method: 'put',
      url,
      data
    })
  },
  
  // DELETE请求
  delete(url, params = {}) {
    return service({
      method: 'delete',
      url,
      params
    })
  },
  
  // PATCH请求
  patch(url, data = {}) {
    return service({
      method: 'patch',
      url,
      data
    })
  },
  
  // 文件上传
  upload(url, formData) {
    return service({
      method: 'post',
      url,
      data: formData,
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  },
  
  // 下载文件
  download(url, params = {}) {
    return service({
      method: 'get',
      url,
      params,
      responseType: 'blob'
    })
  }
}

export default http 