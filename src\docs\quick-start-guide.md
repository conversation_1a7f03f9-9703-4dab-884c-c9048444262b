# WebSocket系统快速启动指南

## 🚀 快速测试

### 1. 启动应用
```bash
npm run dev
# 或
yarn dev
```

### 2. 访问测试页面
在浏览器中访问: `http://localhost:5173/websocket-test`

这个测试页面提供了完整的WebSocket功能测试界面。

### 3. 检查控制台
打开浏览器开发者工具，查看控制台输出，应该能看到：
- WebSocket连接日志
- 菜单数据加载日志
- 路由变化日志

## 🔧 故障排除

### 问题1: "Cannot access 'handleMenuClick' before initialization"
**解决方案**: 已修复，确保函数在使用前定义。

### 问题2: WebSocket连接失败
**检查项**:
1. 确认WebSocket服务器地址正确 (`ws://************:2025/ws/cbi`)
2. 检查服务器是否运行
3. 查看网络连接

**临时解决方案**: 
可以修改 `src/store/modules/websocket.js` 中的 `wsUrl` 为您的实际服务器地址。

### 问题3: 菜单数据加载失败
**解决方案**: 系统会自动使用本地 `menu.js` 作为备用数据源。

### 问题4: 路由映射问题
**检查**: 确保 `menu.js` 中的路径与路由配置一致。

## 📋 测试步骤

### 基础连接测试
1. 打开应用，检查WebSocket是否自动连接
2. 查看右上角的连接状态指示器
3. 如果连接失败，会显示重连进度

### 菜单切换测试
1. 点击不同的菜单项
2. 观察控制台中的topic订阅日志
3. 检查WebSocket状态组件中的当前topic

### 数据接收测试
1. 访问 `/websocket-test` 页面
2. 点击"订阅测试Topic"按钮
3. 点击"查询数据"按钮
4. 观察数据显示区域

### 页面组件测试
1. 访问 `/` (Station页面)
2. 查看左侧的WebSocket数据面板
3. 访问 `/driver` (DriveGat页面)
4. 查看顶部的WebSocket监控面板

## 🎯 关键功能验证

### ✅ 应该正常工作的功能
- [x] WebSocket自动连接
- [x] 菜单点击时topic切换
- [x] 路由变化时topic切换
- [x] 连接状态监控
- [x] 自动重连机制
- [x] 数据分发到页面组件

### ⚠️ 需要服务器支持的功能
- [ ] 实际数据接收 (需要WebSocket服务器)
- [ ] 心跳响应 (需要服务器支持ping/pong)
- [ ] 具体业务数据处理

## 🔍 调试技巧

### 查看WebSocket状态
```javascript
// 在浏览器控制台中执行
console.log('WebSocket状态:', this.$store.state.websocket)
```

### 查看路由映射
```javascript
// 在浏览器控制台中执行
import { debugRouteTopicMap } from '/src/utils/routeTopicMapper.js'
debugRouteTopicMap()
```

### 手动触发操作
```javascript
// 手动订阅topic
this.$store.dispatch('websocket/subscribeTopic', { topic: 'test/topic' })

// 手动查询数据
this.$store.dispatch('websocket/queryData', { topic: 'test/query', params: {} })
```

## 📝 配置说明

### WebSocket服务器地址
文件: `src/store/modules/websocket.js`
```javascript
wsUrl: 'ws://************:2025/ws/cbi'  // 修改为您的服务器地址
```

### 菜单配置
文件: `src/config/menu.js`
- 每个菜单项都应该有 `topic` 字段
- `path` 字段用于路由映射

### 路由配置
文件: `src/router/index.js`
- 确保路由路径与菜单配置中的 `path` 一致

## 🎉 成功标志

如果看到以下日志，说明系统运行正常：
```
WebSocket初始化成功
从服务器获取菜单数据成功: [...]
路由变化: / -> /driver
路由切换订阅topic成功: driver/status
```

## 📞 获取帮助

如果遇到问题：
1. 检查浏览器控制台错误信息
2. 查看网络面板中的WebSocket连接状态
3. 确认服务器端配置
4. 参考 `websocket-system-guide.md` 获取详细文档
