<template>
    <g attr="自动闭塞">
        <template v-for="element in processedData" :key="element.id+'autoBlock'">
        <circle v-if="element.type === 'circle'" :class="getCircleClass(element)" :style="getCircleStyle(element)" :cx="element.x" :cy="element.y" />
        <text v-else-if="element.type === 'text'" :class="textStyle" :x="element.x" :y="element.y">
            {{ element.text }}
        </text>
        <polygon v-else-if="element.type === 'arrow'" :class="arrowStyle" :points="getArrowPoints(element)" />
        </template>
    </g>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  theme: {
    type: Object,
    required: true
  },
  data: {
    type: Array,
    required: true
  },
})

const { circleStyle, textStyle, arrowStyle } = props.theme

const processedData = computed(() => {
  const elements = []
  props.data.forEach((item, itemIndex) => {
    if(item.circleConfig) {
      item.circleConfig.forEach((circle, circleIndex) => {
        elements.push({
          id: `autoBlock-circle-${itemIndex}-${circleIndex}`,
          type: 'circle',
          ...circle
        })
      })
    }
    if(item.textConfig) {
      item.textConfig.forEach((text, textIndex) => {
        elements.push({
          id: `autoBlock-text-${itemIndex}-${textIndex}`,
          type: 'text',
          x: text.x,
          y: text.y,
          text: text.text,
        })
      })
    }
    if(item.arrowConfig) {
      item.arrowConfig.forEach((arrow, arrowIndex) => {
        elements.push({
          id: `autoBlock-arrow-${itemIndex}-${arrowIndex}`,
          type: 'arrow',
          x: arrow.x,
          y: arrow.y,
          dir: arrow.dir,
        })
      })
    }
  })
  return elements
})

// 使用 Map 缓存箭头点计算结果
const arrowPointsCache = new Map()

const getArrowPoints = (arrow) => {
  const { x, y, dir } = arrow;
  
  // 创建缓存键
  const cacheKey = `${x}-${y}-${dir}`
  
  // 检查缓存
  if (arrowPointsCache.has(cacheKey)) {
    return arrowPointsCache.get(cacheKey)
  }
  
  const points = [];
  // 1左，2右
  if(dir === 1){
    points.push(`${x},${y-3}`)
    points.push(`${x-12},${y-3}`)
    points.push(`${x-12},${y-7}`)
    points.push(`${x-19},${y}`)
    points.push(`${x-12},${y+7}`)
    points.push(`${x-12},${y+3}`)
    points.push(`${x},${y+3}`)
  } else if(dir === 2){
    points.push(`${x},${y-3}`)
    points.push(`${x+12},${y-3}`)
    points.push(`${x+12},${y-7}`)
    points.push(`${x+19},${y}`)
    points.push(`${x+12},${y+7}`)
    points.push(`${x+12},${y+3}`)
    points.push(`${x},${y+3}`)
  }
  
  const result = points.join(' ');
  // 缓存结果
  arrowPointsCache.set(cacheKey, result)
  
  return result;
}

const getCircleStyle = (element) => {
  const { cClrFill, cClrStroke } = element
  if(cClrFill && cClrStroke) {
    return {
      fill: `rgb(${cClrFill})`,
      stroke: `rgb(${cClrStroke})`
    }
  } else if(cClrFill) {
    return {
      fill: `rgb(${cClrFill})`,
      stroke: `rgb(${cClrFill})`
    }
  } else {
    return {}
  }
}

const getCircleClass = (element) => {
  let classStr = ''
  if(element.hasOwnProperty('style')) {
    classStr = `circle-style-${element.style}`;
  } else {
    classStr = circleStyle;
  }
  return classStr;
}
</script>

<style scoped></style>