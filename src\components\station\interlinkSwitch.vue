<template>
    <g attr="衔接道岔">
        <template v-for="(item, index) in interlinkSwitchData" :key="'interlinkSwitch'+index">
            <line v-if="item.type === 'line'" :x1="item.x1" :y1="item.y1" :x2="item.x2" :y2="item.y2" :class="lineStyle" />
            <text v-if="item.type === 'text'" :x="item.x" :y="item.y" :class="textStyle">{{ item.text }}</text>
            <polygon v-if="item.type === 'arrow'" :points="getArrowPoints(item)" :class="arrowStyle" />
            <circle v-if="item.type === 'circle'" :cx="item.x" :cy="item.y" :class="getCircleStyle(item)" />
            <circle v-if="item.type === 'indicatorCircle'" :cx="item.x" :cy="item.y" :class="circleStyle3" />
        </template>
    </g>
</template>
<script setup>
import { computed } from 'vue'

const props = defineProps({
    theme: {
        type: Object,
        required: true
    },
    data: {
        type: Array,
        required: true
    }
})

// circleStyle circleStyle2 circleStyle3 列车信号机、调车信号机、表示灯
const { lineStyle, textStyle, arrowStyle, circleStyle, circleStyle2, circleStyle3 } = props.theme

const getCircleStyle = (item) => {
    // 如果有style类，则优先用style类
    if(item.style) {
        return `circle-style-${item.style}` 
    } else {
        if(item.mode === 1) {
            return circleStyle
        } else if(item.mode === 2) {
            return circleStyle2
        } else if(item.mode === 3) {
            return circleStyle3
        }
    }
}

const interlinkSwitchData = computed(() => {
    const element = []
    props.data.forEach(item => {
        if(item.lineConfig) {
            item.lineConfig.forEach(line => {
                element.push({
                    ...line,
                    type: 'line',
                })
            })
        }
        if(item.textConfig) {
            item.textConfig.forEach(text => {
                element.push({
                    ...text,
                    type: 'text',
                })
            })
        }
        if(item.arrowConfig) {
            item.arrowConfig.forEach(arrow => {
                element.push({
                    ...arrow,
                    type: 'arrow',
                })
            })
        }
        if(item.sigCircleConfig) {
            item.sigCircleConfig.forEach(circle => {
                element.push({
                    ...circle,
                    type: 'circle',
                    mode: item.replayMode,
                })
            })
        }
        if(item.circleConfig) {
            item.circleConfig.forEach(circle => {
                element.push({
                    ...circle,
                    type: 'indicatorCircle',
                })
            })
        }
    })
    return element
})

// 使用 Map 缓存箭头点计算结果
const arrowPointsCache = new Map()

const getArrowPoints = (arrow) => {
  const { x, y, dir } = arrow;
  
  // 创建缓存键
  const cacheKey = `${x}-${y}-${dir}`
  
  // 检查缓存
  if (arrowPointsCache.has(cacheKey)) {
    return arrowPointsCache.get(cacheKey)
  }
  
  const points = [];
  // 1左，2右
  if(dir === 1){
    points.push(`${x},${y-3}`)
    points.push(`${x-12},${y-3}`)
    points.push(`${x-12},${y-7}`)
    points.push(`${x-19},${y}`)
    points.push(`${x-12},${y+7}`)
    points.push(`${x-12},${y+3}`)
    points.push(`${x},${y+3}`)
  } else if(dir === 2){
    points.push(`${x},${y-3}`)
    points.push(`${x+12},${y-3}`)
    points.push(`${x+12},${y-7}`)
    points.push(`${x+19},${y}`)
    points.push(`${x+12},${y+7}`)
    points.push(`${x+12},${y+3}`)
    points.push(`${x},${y+3}`)
  }
  
  const result = points.join(' ');
  // 缓存结果
  arrowPointsCache.set(cacheKey, result)
  
  return result;
}
</script>

