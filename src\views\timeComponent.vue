<template>
  <div class="drive-gat-container">
    <h2>时间输入组件演示</h2>
    
    <!-- 时间格式输入 -->
    <div class="demo-section">
      <h3>1. 时间格式 (HH:mm:ss)</h3>
      <TimeInput
        v-model="timeValue"
        format="time"
        label="请选择时间:"
        :show-current-time="true"
        @change="onTimeChange"
      />
      <p>当前时间值: {{ timeValue }}</p>
    </div>

    <!-- 日期格式输入 -->
    <div class="demo-section">
      <h3>2. 日期格式 (YYYY-MM-DD)</h3>
      <TimeInput
        v-model="dateValue"
        format="date"
        label="请选择日期:"
        :show-current-time="true"
        @change="onDateChange"
      />
      <p>当前日期值: {{ dateValue }}</p>
    </div>

    <!-- 禁用状态演示 -->
    <div class="demo-section">
      <h3>3. 禁用状态</h3>
      <TimeInput
        v-model="disabledValue"
        format="time"
        label="禁用的时间输入:"
        :disabled="true"
        :show-current-time="true"
      />
    </div>

    <!-- 无箭头演示 -->
    <div class="demo-section">
      <h3>4. 无箭头模式</h3>
      <TimeInput
        v-model="noArrowValue"
        format="date"
        label="无箭头的日期输入:"
        :show-arrows="false"
        :show-current-time="false"
        custom-placeholder="请输入日期"
      />
    </div>

    <!-- 调试模式演示 -->
    <div class="demo-section">
      <h3>5. 调试模式</h3>
      <TimeInput
        v-model="debugValue"
        format="time"
        label="调试模式:"
        :debug="true"
        :show-current-time="true"
      />
    </div>

    <!-- 手动控制演示 -->
    <div class="demo-section">
      <h3>6. 手动控制</h3>
      <TimeInput
        ref="manualTimeRef"
        v-model="manualValue"
        format="time"
        label="手动控制的时间输入:"
        :show-current-time="false"
      />
      <div style="margin-top: 10px;">
        <button @click="setCurrentTime">设置当前时间</button>
        <button @click="clearTime">清空</button>
        <button @click="focusInput">聚焦</button>
        <button @click="setSpecificTime">设置为 15:30:45</button>
      </div>
      <p>手动控制值: {{ manualValue }}</p>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import TimeInput from '@/components/TimeInput.vue';

// 响应式数据
const timeValue = ref('');
const dateValue = ref('');
const disabledValue = ref('');
const noArrowValue = ref('');
const debugValue = ref('');
const manualValue = ref('');

// 组件引用
const manualTimeRef = ref(null);

// 事件处理函数
const onTimeChange = (value) => {
  console.log('时间变化:', value);
};

const onDateChange = (value) => {
  console.log('日期变化:', value);
};

// 手动控制函数
const setCurrentTime = () => {
  const now = new Date();
  const timeString = `${String(now.getHours()).padStart(2, '0')}:${String(now.getMinutes()).padStart(2, '0')}:${String(now.getSeconds()).padStart(2, '0')}`;
  manualTimeRef.value?.setValue(timeString);
};

const clearTime = () => {
  manualTimeRef.value?.setValue('');
};

const focusInput = () => {
  manualTimeRef.value?.focus();
};

const setSpecificTime = () => {
  manualTimeRef.value?.setValue('15:30:45');
};

</script>

<style scoped>
.drive-gat-container {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
  font-family: Arial, sans-serif;
  overflow: scroll;
  height: 90vh;
}

h2 {
  color: #333;
  border-bottom: 2px solid #409eff;
  padding-bottom: 10px;
  margin-bottom: 30px;
}

h3 {
  color: #666;
  margin-top: 30px;
  margin-bottom: 15px;
}

.demo-section {
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  background: #fafafa;
}

.demo-section p {
  margin-top: 10px;
  color: #666;
  font-size: 14px;
}

button {
  padding: 8px 16px;
  margin-right: 10px;
  margin-bottom: 5px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  background: #fff;
  cursor: pointer;
  transition: all 0.2s;
}

button:hover {
  background: #ecf5ff;
  border-color: #b3d8ff;
  color: #409eff;
}

button:active {
  background: #409eff;
  border-color: #409eff;
  color: #fff;
}
</style>