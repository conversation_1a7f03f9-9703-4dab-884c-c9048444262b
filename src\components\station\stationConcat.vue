<template>
    <g attr="站场连接">
        <template v-for="element in processedData" :key="element.id">
            <circle v-if="element.type === 'circle'" :class="circleStyle" :cx="element.x" :cy="element.y" />
            <line v-if="element.type === 'line'" :class="lineStyle" :x1="element.x1" :y1="element.y1" :x2="element.x2" :y2="element.y2" />
            <polygon v-else-if="element.type === 'arrow'" :class="arrowStyle" :points="getArrowPoints(element)" />
            <text v-else-if="element.type === 'text'" :class="textStyle" :x="element.x" :y="element.y">
                {{ element.text }}
            </text>
        </template>
    </g>
  </template>
<script setup>
import { computed } from 'vue'

const props = defineProps({
  theme: {
    type: Object,
    required: true
  },
  data: {
    type: Array,
    required: true
  },
})

const { circleStyle, textStyle, lineStyle, arrowStyle } = props.theme

const processedData = computed(() => {
  const elements = []
  props.data.forEach((item, itemIndex) => {
    if(item.sigCircleConfig) {
      item.sigCircleConfig.forEach((circle, circleIndex) => {
        elements.push({
          id: `stationConcat-circle-${itemIndex}-${circleIndex}`,
          type: 'circle',
          x: circle.x,
          y: circle.y,
          replayMode: item.replayMode,
        })
      })
    }
    if(item.lineConfig) {
      item.lineConfig.forEach((line, lineIndex) => {
        elements.push({ 
          id: `stationConcat-line-${itemIndex}-${lineIndex}`,
          type: 'line',
          x1: line.x1,
          y1: line.y1,
          x2: line.x2,
          y2: line.y2,
        })
      })
    }
    if(item.arrowConfig) {
      item.arrowConfig.forEach((arrow, arrowIndex) => {
        elements.push({
          id: `stationConcat-arrow-${itemIndex}-${arrowIndex}`,
          type: 'arrow',
          x: arrow.x,
          y: arrow.y,
          dir: arrow.dir,
        })
      })
    }
    if(item.textConfig) {
      item.textConfig.forEach((text, textIndex) => {
        elements.push({
          id: `stationConcat-text-${itemIndex}-${textIndex}`,
          type: 'text',
          x: text.x,
          y: text.y,
          text: text.text,
        })
      })
    }
  })
  return elements
})

// 使用 Map 缓存箭头点计算结果
const arrowPointsCache = new Map()

const getArrowPoints = (arrow) => {
  const { x, y, dir } = arrow;
  
  // 创建缓存键
  const cacheKey = `${x}-${y}-${dir}`
  
  // 检查缓存
  if (arrowPointsCache.has(cacheKey)) {
    return arrowPointsCache.get(cacheKey)
  }
  
  const points = [];
  // 1左，2右
  if(dir === 1){
    points.push(`${x},${y-3}`)
    points.push(`${x-12},${y-3}`)
    points.push(`${x-12},${y-7}`)
    points.push(`${x-19},${y}`)
    points.push(`${x-12},${y+7}`)
    points.push(`${x-12},${y+3}`)
    points.push(`${x},${y+3}`)
  } else if(dir === 2){
    points.push(`${x},${y-3}`)
    points.push(`${x+12},${y-3}`)
    points.push(`${x+12},${y-7}`)
    points.push(`${x+19},${y}`)
    points.push(`${x+12},${y+7}`)
    points.push(`${x+12},${y+3}`)
    points.push(`${x},${y+3}`)
  }
  
  const result = points.join(' ');
  // 缓存结果
  arrowPointsCache.set(cacheKey, result)
  
  return result;
}
</script>

<style scoped></style>