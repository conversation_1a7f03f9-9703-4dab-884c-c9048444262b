export const menuData = [
    {
        "key": "RealTimeStation",
        "menuName": "实时站场",
        "icon": "icon-cbi-station",
        "layout": "2",
        "path": "/station",
        "topic": "station/status",
        "order": 1,
        "hidden": false,
        "isLeaf": false,
        "children": [
            {
                "key": "MainStation",
                "menuName": "主系站场图",
                "path": "/main",
                "topic": "station/main",
                "order": 1,
                "hidden": false,
                "isLeaf": true
            },
            {
                "key": "PrimaryStation",
                "menuName": "Ⅰ系站场图",
                "path": "/primary",
                "topic": "station/primary",
                "order": 2,
                "hidden": false,
                "isLeaf": true
            },
            {
                "key": "SecondaryStation",
                "menuName": "Ⅱ系站场图",
                "path": "/secondary",
                "topic": "station/secondary",
                "order": 3,
                "hidden": false,
                "isLeaf": true
            },
            {
                "key": "DualStation",
                "menuName": "Ⅰ/Ⅱ系站场图",
                "path": "/dual",
                "topic": "station/dual",
                "order": 4,
                "hidden": false,
                "isLeaf": true
            }
        ]
    },
    {
        "key": "DriverCollection",
        "menuName": "驱动采集",
        "icon": "icon-cbi-drive",
        "layout": "2",
        "path": "/driver",
        "topic": "driver/status",
        "order": 2,
        "hidden": false,
        "isLeaf": false,
        "children": [
            {
                "key": "Cabinet",
                "menuName": "机柜",
                "path": "/driver/cabinet",
                "topic": "driver/cabinet/data",
                "order": 1,
                "hidden": false,
                "isLeaf": true,
                "switch": {
                    "target": "/driver/collection",
                    "trigger": "doubleClick"
                }
            },
            {
                "key": "IO",
                "menuName": "驱动采集",
                "path": "/driver/collection",
                "topic": "driver/collection/data",
                "order": 2,
                "hidden": false,
                "isLeaf": true
            }
        ],
        "mutuallyExclusive": [
            "Cabinet",
            "DriveCollection"
        ]
    },
    {
        "key": "NetworkStatus",
        "menuName": "网络状态",
        "icon": "icon-cbi-network",
        "layout": "2",
        "path": "/network",
        "topic": "network/status",
        "order": 3,
        "hidden": false,
        "isLeaf": false,
        "children": []
    },
    {
        "key": "SafetyInterface",
        "menuName": "安全接口",
        "icon": "icon-cbi-interface",
        "layout": "2",
        "path": "/interface",
        "topic": "interface/data",
        "order": 4,
        "hidden": false,
        "isLeaf": false,
        "children": []
    },
    {
        "key": "AlarmDiagnosis",
        "menuName": "报警诊断",
        "icon": "icon-cbi-alarm",
        "layout": "2",
        "path": "/alarm",
        "topic": "alarm/realtime",
        "order": 5,
        "hidden": false,
        "isLeaf": false,
        "children": []
    },
    {
        "key": "InformationQuery",
        "menuName": "信息查询",
        "icon": "icon-cbi-query",
        "layout": "2",
        "path": "/query",
        "topic": "query/requests",
        "order": 6,
        "hidden": false,
        "isLeaf": false,
        "children": []
    },
    {
        "key": "HistoryPlayback",
        "menuName": "历史回放",
        "icon": "icon-cbi-replay",
        "layout": "2",
        "path": "/replay",
        "topic": "replay/stream",
        "order": 7,
        "hidden": false,
        "isLeaf": false,
        "children": []
    },
    {
        "key": "Utilities",
        "menuName": "辅助功能",
        "icon": "icon-cbi-utilities",
        "layout": "2",
        "path": "/utilities",
        "topic": "utilities/status",
        "order": 8,
        "hidden": false,
        "isLeaf": false,
        "children": [
            {
                "key": "SoftwareVersion",
                "menuName": "软件版本信息",
                "path": "/version",
                "topic": "version/info",
                "order": 1,
                "hidden": false,
                "isLeaf": true
            },
            {
                "key": "HardwareList",
                "menuName": "硬件清单信息",
                "path": "/hardware",
                "topic": "hardware/info",
                "order": 2,
                "hidden": false,
                "isLeaf": true
            },
            {
                "key": "ShutdownSystem",
                "menuName": "关闭系统",
                "path": "/shutdown",
                "topic": "shutdown/command",
                "order": 3,
                "hidden": false,
                "isLeaf": true
            }
        ]
    },
    {
        "key": "DataQuery",
        "menuName": "数据查询",
        "icon": "icon-cbi-dataquery",
        "layout": "2",
        "path": "/dataquery",
        "topic": "dataquery/data",
        "order": 9,
        "hidden": true,
        "isLeaf": false,
        "shortcut": "Ctrl+Shift+D",
        "children": [
            {
                "key": "CBIData",
                "menuName": "联锁数据",
                "path": "/cbidata",
                "topic": "dataquery/cbi/query",
                "order": 1,
                "hidden": false,
                "isLeaf": true
            },
            {
                "key": "CCMData",
                "menuName": "通信控制器数据",
                "path": "/ccmdata",
                "topic": "dataquery/ccm/query",
                "order": 2,
                "hidden": false,
                "isLeaf": true
            },
            {
                "key": "OPSData",
                "menuName": "操表机数据",
                "path": "/opsdata",
                "topic": "dataquery/ops/query",
                "order": 3,
                "hidden": false,
                "isLeaf": true
            },
            {
                "key": "CSMData",
                "menuName": "集中监测数据",
                "path": "/csmdata",
                "topic": "dataquery/csm/query",
                "order": 4,
                "hidden": false,
                "isLeaf": true
            }
        ]
    }
]

export default menuData