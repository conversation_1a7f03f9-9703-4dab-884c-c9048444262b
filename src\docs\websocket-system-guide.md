# 集中式WebSocket管理系统使用指南

## 系统概述

本系统实现了一个集中式的WebSocket管理架构，所有WebSocket通信都通过App.vue统一管理，其他组件不直接处理WebSocket代码。

## 核心架构

### 1. WebSocket管理模块 (`src/store/modules/websocket.js`)
- 负责WebSocket连接管理
- 实现消息分发机制
- 提供订阅/取消订阅功能
- 处理连接异常和自动重连
- 支持心跳检测

### 2. App.vue - WebSocket通信中心
- 初始化WebSocket连接
- 监听路由变化，自动切换topic订阅
- 接收WebSocket数据并分发给子组件
- 提供数据和方法给子组件使用

### 3. 路由与Topic映射工具 (`src/utils/routeTopicMapper.js`)
- 根据menu.js配置建立路由与topic的映射关系
- 提供路由切换时的topic查找功能

## 使用方法

### 在页面组件中接收WebSocket数据

```javascript
<script setup>
import { inject, watch } from 'vue'

// 注入App.vue提供的数据和方法
const websocketData = inject('websocketData')
const queryData = inject('queryData')

// 监听WebSocket数据变化
watch(websocketData, (newData) => {
  if (newData) {
    console.log('收到WebSocket数据:', newData)
    // 处理业务逻辑
    handleData(newData)
  }
}, { deep: true })

// 查询数据
const querySpecificData = async () => {
  await queryData('your-topic', {
    filter: 'some-filter',
    timestamp: Date.now()
  })
}
</script>
```

### WebSocket消息格式

发送消息格式：
```json
{
  "topic": "topicStationA",
  "action": "subscribe",
  "params": {
    "filter": "xxx",
    "startTime": 1234567890,
    "endTime": 1234567999
  }
}
```

支持的action类型：
- `subscribe`: 订阅topic
- `unsubscribe`: 取消订阅topic
- `query`: 查询数据
- `ping`: 心跳检测

### 菜单配置

在`src/config/menu.js`中为每个菜单项配置topic：

```javascript
{
  "key": "RealTimeStation",
  "menuName": "实时站场",
  "path": "/station",
  "topic": "station/status",
  "children": [
    {
      "key": "MainStation",
      "menuName": "主系站场图",
      "path": "/main",
      "topic": "station/main"
    }
  ]
}
```

## 功能特性

### 1. 自动订阅管理
- 菜单点击时自动订阅对应topic
- 路由切换时自动切换topic订阅
- 页面离开时自动取消订阅

### 2. 连接健壮性
- 自动重连机制（最多5次）
- 心跳检测（30秒间隔）
- 连接状态实时监控

### 3. 数据分发
- 根据topic自动分发数据到对应组件
- 支持多组件同时接收数据
- 数据变化实时响应

### 4. 状态监控
- WebSocket连接状态显示
- 重连进度提示
- 错误信息展示
- 调试信息输出

## 组件说明

### WebSocketStatus.vue
WebSocket状态监控组件，提供：
- 连接状态显示
- 当前订阅topic显示
- 手动重连功能
- 错误信息清除

### 示例页面组件
- `Station.vue`: 站场页面，演示如何接收站场相关数据
- `DriveGat.vue`: 驱动采集页面，演示如何接收驱动相关数据

## 调试方法

### 1. 查看路由与topic映射
```javascript
import { debugRouteTopicMap } from '@/utils/routeTopicMapper.js'
debugRouteTopicMap() // 在控制台输出映射表
```

### 2. 监控WebSocket状态
使用Vuex DevTools查看websocket模块的状态变化

### 3. 控制台日志
系统会输出详细的连接、订阅、数据接收日志

## 扩展指南

### 添加新页面的WebSocket支持

1. 在menu.js中为新页面配置topic
2. 在页面组件中注入websocketData和queryData
3. 添加数据监听和处理逻辑
4. 根据需要调用queryData方法

### 自定义消息处理

在websocket模块的actions中添加自定义处理逻辑：

```javascript
// 自定义消息处理
customMessageHandler({ commit, state }, { topic, data }) {
  // 处理特定类型的消息
  if (data.type === 'custom') {
    // 自定义处理逻辑
  }
}
```

## 注意事项

1. 确保WebSocket服务器地址正确配置
2. 菜单配置中的topic必须与服务器端一致
3. 页面组件销毁时会自动清理订阅
4. 避免在子组件中直接操作WebSocket连接
5. 使用provide/inject机制传递数据和方法

## 故障排除

### 连接失败
1. 检查WebSocket服务器是否运行
2. 确认服务器地址和端口正确
3. 查看网络连接状态

### 数据接收异常
1. 确认topic配置正确
2. 检查服务器端topic是否存在
3. 查看控制台错误信息

### 重连失败
1. 检查网络连接
2. 确认服务器状态
3. 查看重连次数是否达到上限
