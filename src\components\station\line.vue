<template>
    <div ref="lineContainerRef" class="line-container" @contextmenu.prevent="showContextMenu">
      <svg-pan-zoom
        ref="panZoom"
        class="svg-canvas"
        :zoom-enabled="true"
        :control-icons-enabled="false"
        :fit="true"
        :center="true"
        :min-zoom="0.2"
        :max-zoom="10"
        :zoom-scale-sensitivity="0.5"
        :dbl-click-zoom-enabled="false"
        :mouse-wheel-zoom-enabled="true"
        :prevent-mouse-events-default="true"
        :drag-enabled="true"
        @created="registerSvgPanZoom"
      >
        <svg
          ref="svgElement"
          :height="`${stationSizeY>screenHeight?stationSizeY:screenHeight}px`"
          :width="`${stationSizeX}px`" 
          :viewBox="`0 0 ${stationSizeX} ${stationSizeY}`"
        >
          <!-- 区段 -->
          <Section :theme="theme.section" :data="stationConfigData?.SectionConfig??[]" />
          <!-- 信号机 -->
          <Singnal :theme="theme.singnal" :data="stationConfigData?.SignalConfig??[]" />
          <!-- 调车信号机 -->
          <Dsignal :theme="theme.dSignal" :data="stationConfigData?.DSignalConfig??[]" />
          <!-- 文本 -->
          <Text :theme="theme.text" :data="stationConfigData?.TextConfig??[]" />
          <!-- 道岔 -->
          <Switch :theme="theme.switch" ref="switchRef" :data="stationConfigData?.SwitchConfig??[]" />
          <!-- 64A叠加计轴 -->
          <DA64 :theme="theme.SFDA" :data="stationConfigData?.['64DAxleBlockConfig']??[]" />
          <!-- 64D半自动闭塞信息 -->
          <DB64 :theme="theme.SFDB" :data="stationConfigData?.['64DBlockConfig']??[]" />
          <!-- 按钮 -->
          <StationButton :theme="theme.stationButton" :data="stationConfigData?.ButtonConfig??[]" />
          <!-- 表示灯 -->
          <Indicator :theme="theme.indicator" :data="stationConfigData?.IndicatorConfig??[]" />
          <!-- 非进路 -->
          <NoRoute :theme="theme.noRoute" :data="stationConfigData?.NoRouteConfig??[]" />
          <!-- 到达驼峰 -->
          <ArriveHump :theme="theme.arriveHump" :data="stationConfigData?.ArriveHumpConfig??[]" />
          <!-- 洗车线 -->
          <DepotWashLine :theme="theme.depotWashLine" :data="stationConfigData?.DepotWashlineConfig??[]" />
          <!-- 平溜 -->
          <PlaneState :theme="theme.planeState" :data="stationConfigData?.PlaneStateConfig??[]" />
          <!-- 信号指示灯 -->
          <SignalIndicator :theme="theme.signalIndicator" :data="stationConfigData?.SignalIndicatorConfig??[]" />
          <!-- 简单驼峰 -->
          <SimpleHump :theme="theme.simpleHump" :data="stationConfigData?.SimpleHumpConfig??[]" />
          <!-- 自动闭塞 -->
          <AutoBlock :theme="theme.autoBlock" :data="stationConfigData?.AutoBlockConfig??[]" />
          <!-- 衔接道岔 -->
          <StationConcat :theme="theme.stationConcat" :data="stationConfigData?.StationContactConfig??[]" />
          <!-- 方形边框 -->
          <Box :theme="theme.box" :data="stationConfigData?.BoxConfig??[]" />
          <!-- 坡道 -->
          <Ramp :theme="theme.ramp" :data="stationConfigData?.RampConfig??[]" />
          <!-- 衔接道岔 -->
          <InterlinkSwitch :theme="theme.interlinkSwitch" :data="stationConfigData?.InterlinkSwitchConfig??[]" />
          <!-- 尽头线 -->
          <EndLine :theme="theme.endLine" :data="stationConfigData?.EndLineConfig??[]" />
          <!-- 绝缘节 -->
          <Insulator :theme="theme.insulator" :data="stationConfigData?.InsulatorConfig??[]" />
        </svg>
      </svg-pan-zoom>
      
      <!-- 右键上下文菜单 -->
      <div 
        v-if="contextMenuVisible" 
        class="context-menu" 
        :style="{ left: contextMenuX + 'px', top: contextMenuY + 'px' }"
        @click.stop
      >
        <div class="menu-item" @click="dcShow">道岔分表示</div>
        <div class="menu-item" @click="resetZoom">还原</div>
        <div class="menu-item" @click="zoomIn">放大</div>
        <div class="menu-item" @click="zoomOut">缩小</div>
      </div>
      
      <!-- 遮罩层，点击关闭菜单 -->
      <div 
        v-if="contextMenuVisible" 
        class="context-menu-overlay" 
        @click="hideContextMenu"
      ></div>

      <SwitchSub v-show="switchSubVisible" @closeSwitchSub="closeSwitchSub" :JNumber="JNumber" :XNumber="XNumber" :data="stationConfigData?.SwitchSubConfig??[]" />
    </div>
  </template>
  
  <script setup>
  import dynamicData from './0717Status.js' // 导入动态数据
  import { stationThemeConfig } from '@/config/stationConfig'
  import { ref, onMounted, onBeforeUnmount, getCurrentInstance, nextTick } from 'vue'
  import Insulator from './Insulator.vue'
  import Section from './section.vue'
  import Singnal from './singnal.vue'
  import Dsignal from './dSignal.vue' 
  import Text from './text.vue'
  import Switch from './switch.vue'
  import DA64 from './64DA.vue'
  import DB64 from './64DB.vue'
  import StationButton from './stationButton.vue'
  import Indicator from './indicator.vue'
  import NoRoute from './noRoute.vue'
  import ArriveHump from './arriveHump.vue'
  import DepotWashLine from './depotWashLine.vue'
  import PlaneState from './planeState.vue'
  import SignalIndicator from './signalIndicator.vue'
  import SimpleHump from './simpleHump.vue'
  import AutoBlock from './autoBlock.vue'
  import StationConcat from './stationConcat.vue'
  import Box from './box.vue'
  import Ramp from "./ramp.vue"
  import InterlinkSwitch from "./interlinkSwitch.vue"
  import EndLine from "./endLine.vue"
  import { SvgPanZoom } from 'vue-svg-pan-zoom'
  import SwitchSub from './switchSub.vue'
  // 响应式数据
  const svgElement = ref(null)
  const panZoom = ref(null)
  const svgPanZoomInstance = ref(null)
  const contextMenuVisible = ref(false)
  const contextMenuX = ref(0)
  const contextMenuY = ref(0)
  const lineContainerRef = ref(null)
  const { proxy } = getCurrentInstance()
  const switchRef = ref(null)
  // 静态配置
  const theme = stationThemeConfig.default
  const stationConfigData = ref(null)
  // 站场默认数据
  const stationSizeX = ref(1920)
  const stationSizeY = ref(1080)
  const screenWidth = ref(1920)
  const screenHeight = ref(1080)
  const JNumber = ref(0)
  const XNumber = ref(0)
  const switchSubVisible = ref(false)
  const dcShow = () => {
    // 道岔分表示方法，需要显示道岔分表示列表
    // 点击时首先关闭模态窗
    hideContextMenu()
    switchSubVisible.value = true
  }
  const closeSwitchSub = () => {
    switchSubVisible.value = false
  }

  const getStationData = async () => {
      try {
        const response = await proxy.$http.get('initcfg/station')
        if(response.code === 200) {
          // 处理站场数据
          stationConfigData.value = response.data
          handleStaticData(response.data)
          setTimeout(() => {
            // handleDynamicData()
          }, 2000)
        }
      } catch (error) {
        console.error('获取用户列表失败:', error)
      } finally {
      }
  }

  const handleDynamicData = () => {
    // 遍历dynamicData，根据usIndex找到对应的元素，并更新元素的属性
    const data = dynamicData['A']
    const { 
      IndicatorStatus, 
      DSignalStatus, 
      NoRouteStatus, 
      PlaneStateStatus, 
      SwitchStatus, 
      SignalIndicatorStatus, 
      DepotWashlineStatus,
      LSignalStatus,
      AutoBlockStatus
    } = data;
    const { 
      IndicatorConfig,
      DSignalConfig,
      NoRouteConfig, 
      PlaneStateConfig,
      SwitchConfig, 
      SignalIndicatorConfig, 
      DepotWashlineConfig,
      SignalConfig,
      AutoBlockConfig
    } = stationConfigData.value;

    // 优化：将状态数组转换为以 usIndex 为键的 Map，以便进行 O(1) 复杂度的快速查找
    const indicatorStatusMap = new Map(IndicatorStatus.map(status => [status.usIndex, status]));
    const dSignalStatusMap = new Map(DSignalStatus.map(status => [status.usIndex, status]));
    const noRouteStatusMap = new Map(NoRouteStatus.map(status => [status.usIndex, status]));
    const PlaneStateStatusMap = new Map(PlaneStateStatus.map(status => [status.usIndex, status]));
    const switchStatusMap = new Map(SwitchStatus.map(status => [status.usIndex, status]));
    const signalIndicatorStatusMap = new Map(SignalIndicatorStatus.map(status => [status.usIndex, status]));
    const depotWashlineStatusMap = new Map(DepotWashlineStatus.map(status => [status.usIndex, status]));
    const lSignalStatusMap = new Map(LSignalStatus.map(status => [status.usIndex, status]));
    const autoBlockStatusMap = new Map(AutoBlockStatus.map(status => [status.usIndex, status]));
    IndicatorConfig.forEach(item => {
      const { usIndex } = item;
      const indicatorStatus = indicatorStatusMap.get(usIndex);
      if(indicatorStatus) {
        item.circleConfig.forEach((circle, circleIndex) => {
          circle.cClrFill = indicatorStatus.circStatus[circleIndex].cClrFill;
          circle.cClrStroke = indicatorStatus.circStatus[circleIndex].cClrStroke;
        })
      }
    })
    SignalConfig.forEach(item => {
      const { usIndex } = item;
      const lSignalStatus = lSignalStatusMap.get(usIndex);
      if(lSignalStatus) {
        item.sigCircleConfig.forEach((circle, circleIndex) => {
          circle.cClrFill = lSignalStatus.circStatus[circleIndex].cClrFill;
          circle.cClrStroke = lSignalStatus.circStatus[circleIndex].cClrStroke;
        })
      }
    })
    DSignalConfig.forEach(item => {
      const { usIndex } = item;
      const dSignalStatus = dSignalStatusMap.get(usIndex);
      if(dSignalStatus) {
        item.sigCircleConfig.forEach((circle, circleIndex) => {
          circle.cClrFill = dSignalStatus.circStatus[circleIndex].cClrFill;
          circle.cClrStroke = dSignalStatus.circStatus[circleIndex].cClrStroke;
        })
      }
    })
    NoRouteConfig.forEach(item => {
      const { usIndex } = item;
      const noRouteStatus = noRouteStatusMap.get(usIndex);
      if(noRouteStatus) {
        item.circleConfig.forEach((circle, circleIndex) => {
          circle.cClrFill = noRouteStatus.circStatus[circleIndex].cClrFill;
          circle.cClrStroke = noRouteStatus.circStatus[circleIndex].cClrStroke;
        })
      }
    })
    PlaneStateConfig.forEach(item => {
      const { usIndex } = item;
      const planeStateStatus = PlaneStateStatusMap.get(usIndex);
      if(planeStateStatus) {
        item.circleConfig.forEach((circle, circleIndex) => {
          circle.cClrFill = planeStateStatus.circStatus[circleIndex].cClrFill;
          circle.cClrStroke = planeStateStatus.circStatus[circleIndex].cClrStroke;
        })
      }
    })
    SwitchConfig.forEach(item => {
      const { usIndex } = item;
      const switchStatus = switchStatusMap.get(usIndex);
      if(switchStatus) {
        item.lineConfig.forEach((line, lineIndex) => {
          line.cClrFlash = switchStatus.lineStatus[lineIndex].cClrFlash;
          line.cClr = switchStatus.lineStatus[lineIndex].cClr;
        })
        item.lineConfig2.forEach((line, lineIndex) => {
          line.cClrFlash = switchStatus.lineStatus2[lineIndex]?.cClrFlash||undefined;
          line.cClr = switchStatus.lineStatus2[lineIndex]?.cClr||undefined;
        })
        item.textConfig[0].cClr = switchStatus.textStatus.cClr;
        item.bRedRect = switchStatus.bRedRect;
      }
    })
    SignalIndicatorConfig.forEach(item => {
      const { usIndex } = item;
      const signalIndicatorStatus = signalIndicatorStatusMap.get(usIndex);
      if(signalIndicatorStatus) {
        item.circleConfig.forEach((circle, circleIndex) => {
          circle.cClrFill = signalIndicatorStatus.circStatus[circleIndex].cClrFill;
          circle.cClrStroke = signalIndicatorStatus.circStatus[circleIndex].cClrStroke;
        })
      }
    })
    DepotWashlineConfig.forEach(item => {
      const { usIndex } = item;
      const depotWashlineStatus = depotWashlineStatusMap.get(usIndex);
      if(depotWashlineStatus) {
        item.circleConfig.forEach((circle, circleIndex) => {
          circle.cClrFill = depotWashlineStatus.circStatus[circleIndex].cClrFill;
          circle.cClrStroke = depotWashlineStatus.circStatus[circleIndex].cClrStroke;
        })
      }
    })
    AutoBlockConfig.forEach(item => {
      const { usIndex } = item;
      const autoBlockStatus = autoBlockStatusMap.get(usIndex);
      if(autoBlockStatus) {
        item.circleConfig.forEach((circle, circleIndex) => {
          circle.cClrFill = autoBlockStatus.circStatus[circleIndex]?.cClrFill||undefined;
          circle.cClrStroke = autoBlockStatus.circStatus[circleIndex]?.cClrStroke||undefined;
        })
        // 还有箭头没有处理
      }
    })
  }

  // 处理静态数据
  const handleStaticData = (stationData) => {
    const { StationConfig } = stationData;
    const StationConfigVal = StationConfig[0];
    stationSizeX.value = StationConfigVal?.usStationHPixels || 1920;
    stationSizeY.value = StationConfigVal?.usStationVPixels || 1080;
    JNumber.value = StationConfigVal?.JNum || 0;
    XNumber.value = StationConfigVal?.XNum || 0;
  }

  // 注册 svg-pan-zoom 实例
  const registerSvgPanZoom = (instance) => {
    svgPanZoomInstance.value = instance
  }

  // 显示右键菜单
  const showContextMenu = (event) => {
    contextMenuX.value = event.clientX
    contextMenuY.value = event.clientY
    contextMenuVisible.value = true
  }
  
  // 隐藏右键菜单
  const hideContextMenu = () => {
    contextMenuVisible.value = false
  }
  
  // 放大
  const zoomIn = () => {
    if (svgPanZoomInstance.value) {
      svgPanZoomInstance.value.zoomIn()
    }
    hideContextMenu()
  }
  
  // 缩小
  const zoomOut = () => {
    if (svgPanZoomInstance.value) {
      svgPanZoomInstance.value.zoomOut()
    }
    hideContextMenu()
  }
  
  // 重置缩放
  const resetZoom = () => {
    if (svgPanZoomInstance.value) {
      svgPanZoomInstance.value.reset()
    }
    hideContextMenu()
  }
  
  // 适应屏幕
  const fitToScreen = () => {
    if (svgPanZoomInstance.value) {
      svgPanZoomInstance.value.fit()
      svgPanZoomInstance.value.center()
    }
    hideContextMenu()
  }
  
  // 获取当前缩放和位置信息
  const getCurrentState = () => {
    if (svgPanZoomInstance.value) {
      const zoom = svgPanZoomInstance.value.getZoom()
      const pan = svgPanZoomInstance.value.getPan()
      console.log('缩放:', zoom, '位置:', pan)
      return { zoom, pan }
    }
  }
  
  // 设置特定的缩放和位置
  const setZoomAndPan = (zoom, pan) => {
    if (svgPanZoomInstance.value) {
      svgPanZoomInstance.value.zoom(zoom)
      svgPanZoomInstance.value.pan(pan)
    }
  }
  
  const getScreenSize = () => {
    screenWidth.value = window.innerWidth;
    screenHeight.value = window.innerHeight;
  }

  let flashInterval = null
  // 生命周期钩子
  onMounted(() => {
    getStationData()
    getScreenSize()
    // 监听全局点击事件，用于关闭菜单
    document.addEventListener('click', hideContextMenu)
    if (lineContainerRef.value) {
      lineContainerRef.value.classList.add('flash-off')
    }
    let flashFlag = true
    flashInterval = setInterval(() => {
      if (lineContainerRef.value) {
        lineContainerRef.value.classList.toggle('flash-on')
        lineContainerRef.value.classList.toggle('flash-off')
      }
      switchRef.value.flashway(flashFlag)
      flashFlag = !flashFlag
    }, 500)
  })
  
  onBeforeUnmount(() => {
    // 移除全局事件监听
    document.removeEventListener('click', hideContextMenu)
    clearInterval(flashInterval)
    if (lineContainerRef.value) {
      lineContainerRef.value.classList.remove('flash-on', 'flash-off')
    }
  })
  </script>
  
  <style lang="scss" scoped>  
  .line-container {
    width: 100%;
    height: inherit;
    font-family: 'simsun';
  }
  .svg-canvas {
    width: 100%;
    height: 100%;
    cursor: grab;
    
    &:active {
      cursor: grabbing;
    }
  }
  
  // 右键上下文菜单样式
  .context-menu {
    position: fixed;
    background-color: rgb(192,192,192);
    border-radius: 2px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    z-index: 1000;
    min-width: 120px;
    padding: 4px 0;
    font-size: 14px;
    color: #333;
  }
  
  .menu-item {
    padding: 8px 16px;
    cursor: pointer;
    white-space: nowrap;
    
    &:hover {
      background-color: #e6f7ff;
    }
    
    &.disabled {
      color: #999;
      cursor: not-allowed;
      
      &:hover {
        background-color: transparent;
      }
    }
  }
  
  .menu-divider {
    height: 1px;
    background-color: #e8e8e8;
    margin: 4px 0;
  }
  
  .context-menu-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 999;
    background: transparent;
  }
  </style>