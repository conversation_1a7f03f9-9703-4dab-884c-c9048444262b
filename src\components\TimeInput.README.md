# TimeInput 时间输入组件

一个基于 `vue-imask` 的通用时间输入组件，支持日期和时间两种格式，具备箭头调整、默认当前时间等功能。

## 功能特性

- ✅ 支持 `YYYY-MM-DD` 日期格式
- ✅ 支持 `HH:mm:ss` 时间格式  
- ✅ 默认显示当前时间/日期
- ✅ 手动输入支持
- ✅ 上下箭头按钮调整
- ✅ 光标定位智能调整
- ✅ 禁用状态支持
- ✅ 自定义标签和占位符
- ✅ 事件回调支持
- ✅ 调试模式
- ✅ 完整的 TypeScript 支持

## 基本用法

### 1. 导入组件

```vue
<script setup>
import TimeInput from '@/components/TimeInput.vue';
</script>
```

### 2. 时间格式 (HH:mm:ss)

```vue
<template>
  <TimeInput
    v-model="timeValue"
    format="time"
    label="请选择时间:"
    :show-current-time="true"
    @change="onTimeChange"
  />
</template>

<script setup>
import { ref } from 'vue';
import TimeInput from '@/components/TimeInput.vue';

const timeValue = ref('');

const onTimeChange = (value) => {
  console.log('时间变化:', value);
};
</script>
```

### 3. 日期格式 (YYYY-MM-DD)

```vue
<template>
  <TimeInput
    v-model="dateValue"
    format="date"
    label="请选择日期:"
    :show-current-time="true"
    @change="onDateChange"
  />
</template>

<script setup>
import { ref } from 'vue';
import TimeInput from '@/components/TimeInput.vue';

const dateValue = ref('');

const onDateChange = (value) => {
  console.log('日期变化:', value);
};
</script>
```

## Props 属性

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `modelValue` | String | `''` | v-model 绑定的值 |
| `format` | String | `'time'` | 格式类型：`'time'` 或 `'date'` |
| `label` | String | `''` | 标签文本 |
| `showArrows` | Boolean | `true` | 是否显示上下箭头按钮 |
| `disabled` | Boolean | `false` | 是否禁用 |
| `showCurrentTime` | Boolean | `true` | 是否默认显示当前时间/日期 |
| `customPlaceholder` | String | `''` | 自定义占位符 |
| `debug` | Boolean | `false` | 是否开启调试模式 |

## Events 事件

| 事件名 | 参数 | 说明 |
|--------|------|------|
| `update:modelValue` | `(value: string)` | v-model 值变化时触发 |
| `change` | `(value: string)` | 值变化时触发 |
| `focus` | - | 输入框获得焦点时触发 |
| `blur` | - | 输入框失去焦点时触发 |

## 暴露的方法

通过 `ref` 可以调用以下方法：

| 方法名 | 参数 | 返回值 | 说明 |
|--------|------|--------|------|
| `focus()` | - | - | 聚焦到输入框 |
| `setValue(value)` | `string` | - | 设置值 |
| `getCurrentValue()` | - | `string` | 获取当前值 |

### 使用示例

```vue
<template>
  <TimeInput
    ref="timeInputRef"
    v-model="timeValue"
    format="time"
  />
  <button @click="setCurrentTime">设置当前时间</button>
  <button @click="focusInput">聚焦</button>
</template>

<script setup>
import { ref } from 'vue';
import TimeInput from '@/components/TimeInput.vue';

const timeValue = ref('');
const timeInputRef = ref(null);

const setCurrentTime = () => {
  const now = new Date();
  const timeString = `${String(now.getHours()).padStart(2, '0')}:${String(now.getMinutes()).padStart(2, '0')}:${String(now.getSeconds()).padStart(2, '0')}`;
  timeInputRef.value?.setValue(timeString);
};

const focusInput = () => {
  timeInputRef.value?.focus();
};
</script>
```

## 高级用法

### 1. 禁用状态

```vue
<TimeInput
  v-model="disabledValue"
  format="time"
  :disabled="true"
  :show-current-time="true"
/>
```

### 2. 无箭头模式

```vue
<TimeInput
  v-model="noArrowValue"
  format="date"
  :show-arrows="false"
  :show-current-time="false"
  custom-placeholder="请输入日期"
/>
```

### 3. 调试模式

```vue
<TimeInput
  v-model="debugValue"
  format="time"
  :debug="true"
  :show-current-time="true"
/>
```

## 键盘操作

- **数字键**: 直接输入对应数字
- **方向键**: 移动光标位置
- **上下箭头**: 调整光标所在部分的值
- **Tab**: 移动到下一个可输入区域

## 鼠标操作

- **点击输入框**: 定位光标
- **点击上箭头**: 增加光标所在部分的值
- **点击下箭头**: 减少光标所在部分的值

## 光标位置智能识别

### 时间格式 (HH:mm:ss)
- 位置 0-2: 调整小时 (00-23)
- 位置 3-5: 调整分钟 (00-59)
- 位置 6-8: 调整秒 (00-59)

### 日期格式 (YYYY-MM-DD)
- 位置 0-4: 调整年份 (1900-2999)
- 位置 5-7: 调整月份 (01-12)
- 位置 8-10: 调整日期 (01-31，根据月份自动调整)

## 依赖

- Vue 3
- vue-imask 7.6.1+

## 注意事项

1. 确保项目中已安装 `vue-imask`
2. 日期调整会自动处理月份天数（如二月28/29天）
3. 调试模式仅用于开发环境，生产环境建议关闭
4. 组件支持响应式更新，外部修改 v-model 值会自动同步 