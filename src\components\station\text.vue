<template>
     <g v-for="item in processedData" :key="item.usIndex+'theText'">
        <text v-if="item.textX>0" :class="textStyle" :style="getTextStyle(item)" :x="item.textX" :y="item.textY">{{ item.text }}</text>
     </g>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
    theme: {
        type: Object,
        required: true
    },
    data: {
        type: Array,
        required: true
    }
})

const { textStyle } = props.theme

const processedData = computed(() => {
    return props.data.map(item => {
        const textObj = item.textConfig?.[0]
        return {
            text: textObj?.text,
            textX: textObj?.x,
            textY: textObj?.y,
            size: textObj?.size||10,
            dir: textObj?.dir||1,
            isBold: textObj?.isBold||true,
            isItalic: textObj?.isItalic||false,
            color: textObj?.color||'255,255,255',
        }
    })
})

const getTextStyle = (item) => {
    const styles = {}
    if (item.size) {
        styles.fontSize = `${item.size}px`
    }
    if (item.dir) {
        styles.textAnchor = item.dir === 1 ? 'start' : item.dir === 2 ? 'middle' : 'end'
    }
    if (item.isBold) {
        styles.fontWeight = 'bold'
    }
    if (item.isItalic) {
        styles.fontStyle = 'italic'
    }
    if(item.color) {
        styles.fill = `rgb(${item.color})`
    }
    return styles
}

</script>