<template>
  <div class="nav-menu-container">
    <!-- 一级菜单 -->
    <ul class="level-one-menu">
      <li
        v-for="item in menuData"
        :key="item.menuName"
        :class="['menu-item', { 
          'active': item.menuName === activeTopMenu,
          'has-children': item.children && item.children.length > 0
        }]"
        :style="item.style"
        @click="handleTopMenuClick(item)"
      >
        <!-- 您可以替换成自己的图标 -->
        <!-- <span class="icon-placeholder"></span>  -->
        <el-icon color="#409efc" class="no-inherit">
            <Share />
        </el-icon>
        <span>{{ item.menuName }}</span>
      </li>
    </ul>

    <!-- 二级菜单 -->
    <ul class="level-two-menu" v-if="activeSubMenu.length > 0 && showSubMenu">
      <li
        v-for="subItem in activeSubMenu"
        :key="subItem.menuName"
        :class="['menu-item', { 'active': subItem.menuName === activeSubMenuItem }]"
        @click="handleSubMenuClick(subItem)"
      >
        <span>{{ subItem.menuName }}</span>
      </li>
    </ul>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onBeforeUnmount, getCurrentInstance, inject } from 'vue';
import { useRouter } from 'vue-router';
import { menuData as localMenuData } from '../config/menu.js';

const router = useRouter();

const activeTopMenu = ref('实时站场');
const activeSubMenuItem = ref('主系站场图');
const showSubMenu = ref(true);
const { proxy } = getCurrentInstance()

// 注入App.vue提供的方法
const handleMenuClick = inject('handleMenuClick')

const menuData = ref([]);

const activeSubMenu = computed(() => {
  const activeItem = menuData.value.find(item => item.menuName === activeTopMenu.value);
  return activeItem && activeItem.children ? activeItem.children : [];
});

const handleTopMenuClick = async (item) => {
  // 路由跳转
  if(item.path == "/station") {
    router.push({name: 'station'});
  } else {
    router.push({path: item.path});
  }

  activeTopMenu.value = item.menuName;

  // 如果点击的一级菜单有子菜单，默认选中第一个子菜单
  if (item.children && item.children.length > 0) {
    activeSubMenuItem.value = activeSubMenuItem.value || item.children[0].menuName;
    showSubMenu.value = true;

    // 如果有子菜单，订阅第一个子菜单的topic
    const firstChild = item.children[0];
    if (firstChild.topic && handleMenuClick) {
      await handleMenuClick(firstChild);
    }
  } else {
    activeSubMenuItem.value = null;

    // 如果没有子菜单，直接订阅当前菜单的topic
    if (item.topic && handleMenuClick) {
      await handleMenuClick(item);
    }
  }

  console.log('Clicked top menu:', item.menuName, 'Topic:', item.topic);
};

const handleSubMenuClick = async (subItem) => {
  activeSubMenuItem.value = subItem.menuName;
  showSubMenu.value = false;

  // 订阅子菜单的topic
  if (subItem.topic && handleMenuClick) {
    await handleMenuClick(subItem);
  }

  // 如果子菜单有路径，进行路由跳转
  if (subItem.path) {
    router.push({ path: subItem.path });
  }

  console.log('Clicked sub menu:', subItem.menuName, 'Topic:', subItem.topic);
};

const getMenuData = async () => {
  try {
    const response = await proxy.$http.get('initcfg/menu')
    if(response.code === 200) {
      menuData.value = response.data;
      console.log('从服务器获取菜单数据成功:', response.data);
    } else {
      throw new Error('服务器返回错误代码: ' + response.code);
    }
  } catch (error) {
    console.warn('从服务器获取菜单数据失败，使用本地配置:', error);
    // 使用本地菜单数据作为备用
    menuData.value = localMenuData;
    console.log('使用本地菜单数据:', localMenuData);
  }
}

onMounted(() => {
  getMenuData();
});

onBeforeUnmount(() => {
  console.log('NavMenu before unmount');
});
</script>

<style scoped lang="scss">
.nav-menu-container {
  background-color: var(--color-night-blue);
  font-size: var(--menu-font-size);

  ul {
    list-style: none;
    padding: 0;
    margin: 0;
    display: flex;
    height: var(--header-height);
    border-bottom: 1px solid var(--color-border);
  }

  .level-two-menu {
    background-color: var(--color-night-blue);
  }

  .menu-item {
    min-width: var(--menu-width);
    cursor: pointer;
    color: var(--text-color);
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid #fff;
    white-space: nowrap;
    padding: 0 20px;
    box-sizing: border-box;
    &:hover {
      background-color: #000088;
    }

    &.active {
      background-color: var(--color-active-blue);
      color: var(--text-color);
    }
    .no-inherit {
        padding-right: 6px;
    }
  }

  .icon-placeholder {
    width: 16px;
    height: 16px;
    margin-right: 8px;
  }
}
</style> 