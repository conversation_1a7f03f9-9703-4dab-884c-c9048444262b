<template>
    <div class="common-header">
        <span>{{ deviceInfo }}</span>
        <span>{{ stationName }}</span>
        <span>{{ phoneNumber }}</span>
        <span>{{ currentTime }}</span>
    </div>
</template>

<script>
export default {
    name: 'commonHeader',
    data() {
        return {
            currentTime: '',
            timer: null,
            deviceInfo: '',
            stationName: '',
            phoneNumber: '',
            currentTime: ''
        }
    },
    mounted() {
        this.updateTime();
        this.timer = setInterval(this.updateTime, 1000);
        this.getHeadData();
    },
    beforeDestroy() {
        clearInterval(this.timer);
    },
    methods: {
        updateTime() {
            const now = new Date();
            const year = now.getFullYear();
            const month = (now.getMonth() + 1).toString().padStart(2, '0');
            const day = now.getDate().toString().padStart(2, '0');
            const hours = now.getHours().toString().padStart(2, '0');
            const minutes = now.getMinutes().toString().padStart(2, '0');
            const seconds = now.getSeconds().toString().padStart(2, '0');
            this.currentTime = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
        },
        async getHeadData() {
            try {
                const response = await this.$http.get('initcfg/title')
                if(response.code === 200) {
                    const { SoftName, StationName, Phone, DateTime } = response.data;
                    this.deviceInfo = SoftName.content;
                    this.stationName = StationName.content;
                    this.phoneNumber = Phone.content;
                    this.currentTime = DateTime.content;
                }
            } catch (error) {
                console.error('获取用户列表失败:', error)
            } finally {
            }
        }
    }
}
</script>

<style lang="scss" scoped>
.common-header {
    background-color: var(--color-night-blue);
    font-size: var(--header-font-size);
    height: var(--header-height);
    line-height: var(--header-height);
    color: #fff;
    padding: 0 10px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid var(--color-border);
}
@media screen and (max-width: 800px) {
    .common-header {
        // font-size: 1.2rem;
        flex-wrap: wrap;
        height: calc(var(--header-height) * 4);
        span {
            width: 100%;
        }
    }
}
</style>