// API接口地址统一管理
const API = {
  // 用户相关接口
  USER: {
    LOGIN: '/user/login',           // 用户登录
    LOGOUT: '/user/logout',         // 用户登出
    REGISTER: '/user/register',     // 用户注册
    INFO: '/user/info',             // 获取用户信息
    UPDATE: '/user/update',         // 更新用户信息
  }
  
  // 可以在这里添加更多的接口分组
  // 例如：
  // PRODUCT: {
  //   LIST: '/product/list',
  //   DETAIL: '/product/detail',
  //   CREATE: '/product/create',
  //   UPDATE: '/product/update',
  //   DELETE: '/product/delete',
  // }
}

export default API 