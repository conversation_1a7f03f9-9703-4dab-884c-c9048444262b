<script setup>
import { onMounted, onBeforeUnmount, watch, provide, ref } from 'vue';
import { RouterView, useRoute } from 'vue-router';
import { useStore } from 'vuex';
import commonHeader from './components/commonHeader.vue';
import NavMenu from './components/NavMenu.vue';
import WebSocketStatus from './components/WebSocketStatus.vue';
import { getTopicByRoute, getDefaultTopic } from './utils/routeTopicMapper.js';

const route = useRoute()
const store = useStore()

// 当前页面的WebSocket数据
const currentPageData = ref(null)

// 处理菜单点击事件
const handleMenuClick = async (menuItem) => {
  console.log('菜单点击:', menuItem)

  // 如果菜单项有topic信息，进行订阅
  if (menuItem.topic) {
    try {
      await store.dispatch('websocket/subscribeTopic', {
        topic: menuItem.topic
      })
      console.log('菜单切换订阅topic成功:', menuItem.topic)
    } catch (error) {
      console.error('菜单切换订阅topic失败:', error)
    }
  }
}

// 查询数据方法
// function queryData(topic, params = {}) {
//   return store.dispatch('websocket/queryData', { topic, params })
// }

// 提供给子组件的数据和方法
provide('websocketData', currentPageData)
provide('queryData', queryData)
provide('handleMenuClick', handleMenuClick)

// 初始化WebSocket连接
const initWebSocket = async () => {
  try {
    await store.dispatch('websocket/initWebSocket')
    console.log('WebSocket初始化成功')

    // 订阅当前路由对应的topic
    const currentTopic = getTopicByRoute(route.path) || getDefaultTopic()
    console.log(currentTopic)
    if (currentTopic) {
      await subscribeCurrentTopic(currentTopic)
    }
  } catch (error) {
    console.error('WebSocket初始化失败:', error)
  }
}

// 订阅当前topic
const subscribeCurrentTopic = async (topic) => {
  try {
    await store.dispatch('websocket/subscribeTopic', { topic })
    console.log('订阅topic成功:', topic)
  } catch (error) {
    console.error('订阅topic失败:', error)
  }
}

// 查询数据方法
function queryData(topic, params = {}) {
  return store.dispatch('websocket/queryData', { topic, params })
}

// 监听路由变化
watch(() => route.path, async (newPath, oldPath) => {
  console.log('路由变化:', oldPath, '->', newPath)

  const newTopic = getTopicByRoute(newPath)
  const oldTopic = getTopicByRoute(oldPath)

  // 如果新旧topic不同，进行切换
  if (newTopic && newTopic !== oldTopic) {
    try {
      await store.dispatch('websocket/subscribeTopic', { topic: newTopic })
      console.log('路由切换订阅topic成功:', newTopic)
    } catch (error) {
      console.error('路由切换订阅topic失败:', error)
    }
  }
}, { immediate: false })

// 监听WebSocket数据变化
watch(() => store.getters['websocket/getAllMessageData'], (newData) => {
  const currentTopic = store.getters['websocket/currentTopic']
  if (currentTopic && newData[currentTopic]) {
    currentPageData.value = newData[currentTopic]
    console.log('当前页面数据更新:', currentPageData.value)
  }
}, { deep: true })

onMounted(() => {
  // initWebSocket()
})

onBeforeUnmount(() => {
  // 关闭WebSocket连接
  store.dispatch('websocket/closeWebSocket')
})
</script>

<template>
  <div class="app-container">
    <commonHeader />
    <NavMenu />

    <!-- WebSocket连接状态指示器 -->
    <div v-if="!store.getters['websocket/isConnected']" class="connection-status">
      <span class="status-indicator offline"></span>
      <span>WebSocket连接断开</span>
      <span v-if="store.getters['websocket/getReconnectInfo'].isReconnecting">
        (重连中... {{ store.getters['websocket/getReconnectInfo'].count }}/{{ store.getters['websocket/getReconnectInfo'].maxCount }})
      </span>
    </div>

    <!-- WebSocket状态监控组件 -->
    <WebSocketStatus />

    <RouterView />
  </div>
</template>

<style scoped>
.app-container {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
}

/* WebSocket连接状态指示器 */
.connection-status {
  position: fixed;
  top: 10px;
  right: 10px;
  background-color: rgba(255, 0, 0, 0.8);
  color: white;
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 12px;
  z-index: 9999;
  display: flex;
  align-items: center;
  gap: 6px;
}

.status-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  display: inline-block;
}

.status-indicator.offline {
  background-color: #ff4444;
  animation: blink 1s infinite;
}

@keyframes blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0.3; }
}

header {
  line-height: 1.5;
  max-height: 100vh;
}

.logo {
  display: block;
  margin: 0 auto 2rem;
}

nav {
  width: 100%;
  font-size: 12px;
  text-align: center;
  margin-top: 2rem;
}

nav a.router-link-exact-active {
  color: var(--color-text);
}

nav a.router-link-exact-active:hover {
  background-color: transparent;
}

nav a {
  display: inline-block;
  padding: 0 1rem;
  border-left: 1px solid var(--color-border);
}

nav a:first-of-type {
  border: 0;
}

@media (min-width: 1024px) {
  header {
    display: flex;
    place-items: center;
    padding-right: calc(var(--section-gap) / 2);
  }

  .logo {
    margin: 0 2rem 0 0;
  }

  header .wrapper {
    display: flex;
    place-items: flex-start;
    flex-wrap: wrap;
  }

  nav {
    text-align: left;
    margin-left: -1rem;
    font-size: 1rem;

    padding: 1rem 0;
    margin-top: 1rem;
  }
}
</style>
