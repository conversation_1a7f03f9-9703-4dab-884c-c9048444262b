<template>
  <div class="websocket-status" v-if="showStatus">
    <div class="status-header">
      <span class="status-title">WebSocket状态</span>
      <button @click="toggleExpanded" class="toggle-btn">
        {{ isExpanded ? '收起' : '展开' }}
      </button>
      <button @click="hideStatus" class="close-btn">×</button>
    </div>
    
    <div v-if="isExpanded" class="status-content">
      <!-- 连接状态 -->
      <div class="status-item">
        <span class="label">连接状态:</span>
        <span :class="['status-value', connectionStatusClass]">
          {{ connectionStatusText }}
        </span>
      </div>
      
      <!-- 当前订阅的topic -->
      <div class="status-item" v-if="currentTopic">
        <span class="label">当前Topic:</span>
        <span class="status-value">{{ currentTopic }}</span>
      </div>
      
      <!-- 重连信息 -->
      <div class="status-item" v-if="reconnectInfo.isReconnecting">
        <span class="label">重连状态:</span>
        <span class="status-value warning">
          重连中... ({{ reconnectInfo.count }}/{{ reconnectInfo.maxCount }})
        </span>
      </div>
      
      <!-- 错误信息 -->
      <div class="status-item" v-if="error">
        <span class="label">错误信息:</span>
        <span class="status-value error">{{ error }}</span>
      </div>
      
      <!-- 操作按钮 -->
      <div class="status-actions">
        <button 
          @click="reconnect" 
          :disabled="isConnected || reconnectInfo.isReconnecting"
          class="action-btn"
        >
          手动重连
        </button>
        <button 
          @click="disconnect" 
          :disabled="!isConnected"
          class="action-btn"
        >
          断开连接
        </button>
        <button @click="clearError" v-if="error" class="action-btn">
          清除错误
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useStore } from 'vuex'

const store = useStore()
const showStatus = ref(true)
const isExpanded = ref(false)

// 计算属性
const isConnected = computed(() => store.getters['websocket/isConnected'])
const currentTopic = computed(() => store.getters['websocket/currentTopic'])
const error = computed(() => store.getters['websocket/getError'])
const reconnectInfo = computed(() => store.getters['websocket/getReconnectInfo'])

const connectionStatusClass = computed(() => {
  if (isConnected.value) return 'connected'
  if (reconnectInfo.value.isReconnecting) return 'reconnecting'
  return 'disconnected'
})

const connectionStatusText = computed(() => {
  if (isConnected.value) return '已连接'
  if (reconnectInfo.value.isReconnecting) return '重连中...'
  return '已断开'
})

// 方法
const toggleExpanded = () => {
  isExpanded.value = !isExpanded.value
}

const hideStatus = () => {
  showStatus.value = false
}

const reconnect = () => {
  store.dispatch('websocket/initWebSocket')
}

const disconnect = () => {
  store.dispatch('websocket/closeWebSocket')
}

const clearError = () => {
  store.commit('websocket/CLEAR_ERROR')
}
</script>

<style scoped>
.websocket-status {
  position: fixed;
  top: 60px;
  right: 10px;
  background: rgba(0, 0, 0, 0.9);
  color: white;
  border-radius: 6px;
  min-width: 250px;
  max-width: 400px;
  z-index: 9998;
  font-size: 12px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

.status-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 6px 6px 0 0;
}

.status-title {
  font-weight: bold;
}

.toggle-btn, .close-btn {
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 11px;
}

.toggle-btn:hover, .close-btn:hover {
  background: rgba(255, 255, 255, 0.2);
}

.close-btn {
  font-size: 16px;
  font-weight: bold;
}

.status-content {
  padding: 12px;
}

.status-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  align-items: center;
}

.label {
  color: #ccc;
  margin-right: 8px;
}

.status-value {
  font-weight: bold;
}

.status-value.connected {
  color: #4CAF50;
}

.status-value.disconnected {
  color: #f44336;
}

.status-value.reconnecting {
  color: #ff9800;
}

.status-value.warning {
  color: #ff9800;
}

.status-value.error {
  color: #f44336;
  word-break: break-all;
}

.status-actions {
  margin-top: 12px;
  display: flex;
  gap: 6px;
  flex-wrap: wrap;
}

.action-btn {
  background: #2196F3;
  color: white;
  border: none;
  padding: 4px 8px;
  border-radius: 3px;
  cursor: pointer;
  font-size: 11px;
}

.action-btn:hover:not(:disabled) {
  background: #1976D2;
}

.action-btn:disabled {
  background: #666;
  cursor: not-allowed;
}
</style>
