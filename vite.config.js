import { fileURLToPath, URL } from 'node:url'

import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import vueDevTools from 'vite-plugin-vue-devtools'

// https://vite.dev/config/
export default defineConfig({
  plugins: [
    vue(),
    // vueDevTools(),
  ],
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url)),
      '@assets': fileURLToPath(new URL('./src/assets', import.meta.url)),
    },
  },
  server: {
    host: '0.0.0.0', // 监听所有网络接口，允许外部访问
    port: 8888,      // 指定端口号
    // open: true,      // 启动时自动打开浏览器
    cors: true,      // 启用CORS
    // 如果需要https，可以启用以下配置
    // https: true,
    proxy: {
      // '/api': {
      //   target: 'http://************:8081',
      //   changeOrigin: true,
      //   secure: false,
      //   ws: true, // 支持websocket
      //   configure: (proxy, options) => {
      //     proxy.on('error', (err, req, res) => {
      //       console.log('proxy error', err);
      //     });
      //     proxy.on('proxyReq', (proxyReq, req, res) => {
      //       console.log('Sending Request to the Target:', req.method, req.url);
      //     });
      //     proxy.on('proxyRes', (proxyRes, req, res) => {
      //       console.log('Received Response from the Target:', proxyRes.statusCode, req.url);
      //     });
      //   }
      //   // rewrite: (path) => path.replace(/^\/api/, '')
      // }
    }
  },
})
