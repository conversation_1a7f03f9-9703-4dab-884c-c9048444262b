<script setup>
import { computed } from 'vue'

const props = defineProps({
  theme: {
    type: Object,
    required: true
  },
  data: {
    type: Array,
    required: true
  },
})

const { circleStyle1, circleStyle2, textStyle, lineStyle } = props.theme

const processedData = computed(() => {
  const elements = []
  props.data.forEach((item, itemIndex) => {
    if(item.sigCircleConfig) {
      item.sigCircleConfig.forEach((circle, circleIndex) => {
        elements.push({
          id: `simpleHump-circle-${itemIndex}-${circleIndex}`,
          type: 'circle',
          x: circle.x,
          y: circle.y,
        })
      })
    }
    if(item.circleConfig) {
      item.circleConfig.forEach((circle, circleIndex) => {
        elements.push({
          id: `simpleHump-circle2-${itemIndex}-${circleIndex}`,
          type: 'circle2',
          x: circle.x,
          y: circle.y,
        })
      })
    }
    if(item.lineConfig) {
      item.lineConfig.forEach((line, lineIndex) => {
        elements.push({
          id: `simpleHump-line-${itemIndex}-${lineIndex}`,
          type: 'line',
          x1: line.x1,
          y1: line.y1,
          x2: line.x2,
          y2: line.y2,
        })
      })
    }
    if(item.textConfig) {
      item.textConfig.forEach((text, textIndex) => {
        elements.push({
          id: `simpleHump-text-${itemIndex}-${textIndex}`,
          type: 'text',
          x: text.x,
          y: text.y,
          text: text.text,
        })
      })
    }
  })
  return elements
})
</script>

<template>
  <g attr="简易驼峰">
    <template v-for="element in processedData" :key="element.id">
      <circle v-if="element.type === 'circle'" :class="[circleStyle1]" :cx="element.x" :cy="element.y" />
      <circle v-else-if="element.type === 'circle2'" :class="circleStyle2" :cx="element.x" :cy="element.y" />
      <line v-else-if="element.type === 'line'" :class="lineStyle" :x1="element.x1" :y1="element.y1" :x2="element.x2" :y2="element.y2" />
      <text v-else-if="element.type === 'text'" :class="textStyle" :x="element.x" :y="element.y">
        {{ element.text }}
      </text>
    </template>
  </g>
</template>

<style scoped></style>