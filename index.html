<!DOCTYPE html>
<html lang="">
  <head>
    <meta charset="UTF-8">
    <!-- <link rel="icon" href="/favicon.ico"> -->
    <link rel="icon" href="/logo.png">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CBI联锁</title>
    <link rel="stylesheet" href="/src/styles/themes/default.scss">
  </head>
  <body>
    <div id="app"></div>
    <script type="module" src="/src/main.js"></script>
    <script>
      let filePath = '/requireUrl.js';
      function readFile (filePath) {
        let xhr = null;
        if (window.XMLHttpRequest) {
            xhr = new XMLHttpRequest()
        } else {
            xhr = new ActiveXObject('Microsoft.XMLHTTP')
        }
        const okStatus = document.location.protocol === 'file' ? 0 : 200;
        xhr.open('GET', filePath, false);
        xhr.overrideMimeType('text/html;charset=utf-8');
        xhr.send(null);
        // console.log(xhr.responseText)
        let baseURL = xhr.responseText
        return baseURL
      }
      let baseURLContent = readFile(filePath);
      
      // 处理内容：移除 source map 注释和多余字符
      let cleanBaseURL = baseURLContent
        .split('\n')[0]                    // 取第一行，忽略 source map 注释
        .replace(/['"]/g, '')              // 移除引号
        .trim();                           // 移除空白字符
      
      sessionStorage.setItem('baseURL', cleanBaseURL);
    </script>
  </body>
</html>
