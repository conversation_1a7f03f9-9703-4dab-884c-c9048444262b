/**
 * 状态持久化工具函数
 * 用于Vuex状态与localStorage之间的交互
 */

const STORAGE_PREFIX = 'CBI_'

/**
 * 保存状态到localStorage
 * @param {string} key 存储键名
 * @param {any} value 要存储的值
 */
export const saveToStorage = (key, value) => {
  try {
    const prefixedKey = STORAGE_PREFIX + key
    const serializedValue = JSON.stringify(value)
    localStorage.setItem(prefixedKey, serializedValue)
    console.log(`状态已保存: ${key}`, value)
  } catch (error) {
    console.error(`保存状态失败: ${key}`, error)
  }
}

/**
 * 从localStorage读取状态
 * @param {string} key 存储键名
 * @param {any} defaultValue 默认值
 * @returns {any} 读取的值或默认值
 */
export const loadFromStorage = (key, defaultValue = null) => {
  try {
    const prefixedKey = STORAGE_PREFIX + key
    const stored = localStorage.getItem(prefixedKey)
    if (stored === null) {
      return defaultValue
    }
    const parsed = JSON.parse(stored)
    console.log(`状态已恢复: ${key}`, parsed)
    return parsed
  } catch (error) {
    console.error(`读取状态失败: ${key}`, error)
    return defaultValue
  }
}

/**
 * 删除localStorage中的状态
 * @param {string} key 存储键名
 */
export const removeFromStorage = (key) => {
  try {
    const prefixedKey = STORAGE_PREFIX + key
    localStorage.removeItem(prefixedKey)
    console.log(`状态已删除: ${key}`)
  } catch (error) {
    console.error(`删除状态失败: ${key}`, error)
  }
}

/**
 * 清空所有CBI相关的localStorage数据
 */
export const clearAllStorage = () => {
  try {
    const keys = Object.keys(localStorage)
    const cbiKeys = keys.filter(key => key.startsWith(STORAGE_PREFIX))
    cbiKeys.forEach(key => localStorage.removeItem(key))
    console.log(`已清空所有CBI状态数据，共${cbiKeys.length}项`)
  } catch (error) {
    console.error('清空状态数据失败', error)
  }
}

/**
 * 获取所有CBI相关的localStorage数据
 * @returns {Object} 所有存储的数据
 */
export const getAllStorageData = () => {
  try {
    const keys = Object.keys(localStorage)
    const cbiKeys = keys.filter(key => key.startsWith(STORAGE_PREFIX))
    const data = {}
    
    cbiKeys.forEach(prefixedKey => {
      const key = prefixedKey.replace(STORAGE_PREFIX, '')
      const value = loadFromStorage(key)
      if (value !== null) {
        data[key] = value
      }
    })
    
    return data
  } catch (error) {
    console.error('获取所有状态数据失败', error)
    return {}
  }
}

/**
 * 检查localStorage是否可用
 * @returns {boolean} 是否可用
 */
export const isStorageAvailable = () => {
  try {
    const testKey = STORAGE_PREFIX + 'test'
    localStorage.setItem(testKey, 'test')
    localStorage.removeItem(testKey)
    return true
  } catch (error) {
    console.warn('localStorage不可用', error)
    return false
  }
}

/**
 * 创建防抖的保存函数
 * @param {Function} saveFn 保存函数
 * @param {number} delay 延迟时间（毫秒）
 * @returns {Function} 防抖后的保存函数
 */
export const createDebouncedSave = (saveFn, delay = 500) => {
  let timeoutId = null
  
  return (...args) => {
    clearTimeout(timeoutId)
    timeoutId = setTimeout(() => {
      saveFn(...args)
    }, delay)
  }
}

// 模块特定的存储键名常量
export const STORAGE_KEYS = {
  // DriveGat模块
  DRIVE_GAT_TIME_VALUE: 'driveGat_timeValue',
  DRIVE_GAT_DATE_VALUE: 'driveGat_dateValue',
  DRIVE_GAT_SELECTED_DEVICE: 'driveGat_selectedDevice',
  DRIVE_GAT_SEARCH_CONDITIONS: 'driveGat_searchConditions',
  
  // HistoryPlayback模块
  HISTORY_PLAYBACK_REPLAY_TARGET: 'historyPlayback_replayTarget',
  HISTORY_PLAYBACK_IS_PAUSED: 'historyPlayback_isPaused',
  HISTORY_PLAYBACK_SPEED: 'historyPlayback_speed',
  HISTORY_PLAYBACK_SEARCH_CONDITIONS: 'historyPlayback_searchConditions',
  HISTORY_PLAYBACK_USER_PREFERENCES: 'historyPlayback_userPreferences',
}

export default {
  saveToStorage,
  loadFromStorage,
  removeFromStorage,
  clearAllStorage,
  getAllStorageData,
  isStorageAvailable,
  createDebouncedSave,
  STORAGE_KEYS
} 