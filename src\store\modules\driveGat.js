// DriveGat 模块状态管理
const state = {
  // 时间输入相关状态
  timeValue: '',
  dateValue: '',
  disabledValue: '',
  noArrowValue: '',
  debugValue: '',
  manualValue: '',
  
  // 页面配置状态
  showCurrentTime: true,
  debugMode: false,
  
  // 其他状态（可根据实际需求扩展）
  selectedDevice: '',
  searchConditions: {
    keyword: '',
    dateRange: [],
    status: ''
  }
}

const mutations = {
  // 更新时间值
  SET_TIME_VALUE(state, value) {
    state.timeValue = value
  },
  SET_DATE_VALUE(state, value) {
    state.dateValue = value
  },
  SET_DISABLED_VALUE(state, value) {
    state.disabledValue = value
  },
  SET_NO_ARROW_VALUE(state, value) {
    state.noArrowValue = value
  },
  SET_DEBUG_VALUE(state, value) {
    state.debugValue = value
  },
  SET_MANUAL_VALUE(state, value) {
    state.manualValue = value
  },
  
  // 更新配置
  SET_SHOW_CURRENT_TIME(state, value) {
    state.showCurrentTime = value
  },
  SET_DEBUG_MODE(state, value) {
    state.debugMode = value
  },
  
  // 更新选中设备
  SET_SELECTED_DEVICE(state, device) {
    state.selectedDevice = device
  },
  
  // 更新搜索条件
  SET_SEARCH_CONDITIONS(state, conditions) {
    state.searchConditions = { ...state.searchConditions, ...conditions }
  },
  
  // 重置搜索条件
  RESET_SEARCH_CONDITIONS(state) {
    state.searchConditions = {
      keyword: '',
      dateRange: [],
      status: ''
    }
  }
}

const actions = {
  // 保存时间值
  saveTimeValue({ commit }, value) {
    commit('SET_TIME_VALUE', value)
    // 可以在这里添加本地存储逻辑
    localStorage.setItem('driveGat_timeValue', value)
  },
  
  saveDateValue({ commit }, value) {
    commit('SET_DATE_VALUE', value)
    localStorage.setItem('driveGat_dateValue', value)
  },
  
  // 保存设备选择
  saveSelectedDevice({ commit }, device) {
    commit('SET_SELECTED_DEVICE', device)
    localStorage.setItem('driveGat_selectedDevice', device)
  },
  
  // 保存搜索条件
  saveSearchConditions({ commit }, conditions) {
    commit('SET_SEARCH_CONDITIONS', conditions)
    localStorage.setItem('driveGat_searchConditions', JSON.stringify(conditions))
  },
  
  // 从本地存储恢复状态
  restoreFromStorage({ commit }) {
    const timeValue = localStorage.getItem('driveGat_timeValue')
    const dateValue = localStorage.getItem('driveGat_dateValue')
    const selectedDevice = localStorage.getItem('driveGat_selectedDevice')
    const searchConditions = localStorage.getItem('driveGat_searchConditions')
    
    if (timeValue) commit('SET_TIME_VALUE', timeValue)
    if (dateValue) commit('SET_DATE_VALUE', dateValue)
    if (selectedDevice) commit('SET_SELECTED_DEVICE', selectedDevice)
    if (searchConditions) {
      try {
        commit('SET_SEARCH_CONDITIONS', JSON.parse(searchConditions))
      } catch (e) {
        console.warn('解析搜索条件失败:', e)
      }
    }
  }
}

const getters = {
  // 获取所有时间相关状态
  allTimeValues: (state) => ({
    timeValue: state.timeValue,
    dateValue: state.dateValue,
    manualValue: state.manualValue
  }),
  
  // 检查是否有未保存的更改
  hasUnsavedChanges: (state) => {
    return state.timeValue || state.dateValue || state.selectedDevice
  },
  
  // 格式化搜索条件用于显示
  formattedSearchConditions: (state) => {
    const conditions = []
    if (state.searchConditions.keyword) {
      conditions.push(`关键词: ${state.searchConditions.keyword}`)
    }
    if (state.searchConditions.dateRange.length > 0) {
      conditions.push(`日期范围: ${state.searchConditions.dateRange.join(' - ')}`)
    }
    if (state.searchConditions.status) {
      conditions.push(`状态: ${state.searchConditions.status}`)
    }
    return conditions
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters
} 