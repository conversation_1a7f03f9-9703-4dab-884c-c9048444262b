<template>
  <g attr="非进路">
    <template v-for="element in processedData" :key="element.id+'noRoute'">
      <circle v-if="element.type === 'circle'" :class="circleStyle" :style="getCircleStyle(element)" :cx="element.x" :cy="element.y" />
      <text v-else-if="element.type === 'text'" :class="textStyle" :x="element.x" :y="element.y">
        {{ element.text }}
      </text>
      <text v-else-if="element.type === 'redText'" :class="redTextStyle" :x="element.x" :y="element.y">
        {{ element.text }}
      </text>
    </template>
  </g>
</template>

<script setup>
import { computed, onMounted } from 'vue'

const props = defineProps({
  data: {
    type: Array,
    required: true
  },
  theme: {
    type: Object,
    required: true
  }
})

const { circleStyle, textStyle, redTextStyle } = props.theme

const getCircleStyle = (circle) => {
  const styles = {}
  if(circle.cClrFill) {
    styles.fill = `rgb(${circle.cClrFill})`
  }
  if(circle.cClrStroke) {
    styles.stroke = `rgb(${circle.cClrStroke})`
  }
  return styles
}

const processedData = computed(() => {  
  const elements = []
  props.data.forEach((item, itemIndex) => {
    if(item.circleConfig) {
      item.circleConfig.forEach((circle, circleIndex) => {
        elements.push({
          id: `circle-${itemIndex}-${circleIndex}`,
          type: 'circle',
          ...circle,
        })
      })
    }

    if(item.textConfig) {
      item.textConfig.forEach((text, textIndex) => {
        elements.push({
          id: `text-${itemIndex}-${textIndex}`,
          type: 'text',
          ...text,
        })
      })
    }

    if(item.redTextConfig) {
      item.redTextConfig.forEach((redText, redTextIndex) => {
        elements.push({
          id: `redText-${itemIndex}-${redTextIndex}`,
          type: 'redText',
          ...redText,
        })
      })
    }
  })
  return elements
})


</script>