// 使用示例
// 以下是在Vue组件中使用$http的示例代码

/*
在Vue组件中的使用方法：

1. 选项式API (Options API) 使用方式:
export default {
  name: 'ExampleComponent',
  data() {
    return {
      userList: [],
      loading: false
    }
  },
  methods: {
    // GET请求示例
    async getUserList() {
      try {
        this.loading = true
        const response = await this.$http.get('/user/list', {
          page: 1,
          size: 10
        })
        this.userList = response.data
      } catch (error) {
        console.error('获取用户列表失败:', error)
      } finally {
        this.loading = false
      }
    },
    
    // POST请求示例
    async createUser(userData) {
      try {
        const response = await this.$http.post('/user/create', userData)
        console.log('创建用户成功:', response)
        this.getUserList() // 重新获取列表
      } catch (error) {
        console.error('创建用户失败:', error)
      }
    },
    
    // PUT请求示例
    async updateUser(userId, userData) {
      try {
        const response = await this.$http.put(`/user/update/${userId}`, userData)
        console.log('更新用户成功:', response)
      } catch (error) {
        console.error('更新用户失败:', error)
      }
    },
    
    // DELETE请求示例
    async deleteUser(userId) {
      try {
        await this.$http.delete(`/user/delete/${userId}`)
        console.log('删除用户成功')
        this.getUserList() // 重新获取列表
      } catch (error) {
        console.error('删除用户失败:', error)
      }
    },
    
    // 文件上传示例
    async uploadFile(file) {
      try {
        const formData = new FormData()
        formData.append('file', file)
        const response = await this.$http.upload('/upload', formData)
        console.log('文件上传成功:', response)
      } catch (error) {
        console.error('文件上传失败:', error)
      }
    }
  },
  mounted() {
    this.getUserList()
  }
}

2. 组合式API (Composition API) 使用方式:
import { ref, onMounted, getCurrentInstance } from 'vue'

export default {
  setup() {
    const { proxy } = getCurrentInstance()
    const userList = ref([])
    const loading = ref(false)
    
    // GET请求示例
    const getUserList = async () => {
      try {
        loading.value = true
        const response = await proxy.$http.get('/user/list', {
          page: 1,
          size: 10
        })
        userList.value = response.data
      } catch (error) {
        console.error('获取用户列表失败:', error)
      } finally {
        loading.value = false
      }
    }
    
    // POST请求示例
    const createUser = async (userData) => {
      try {
        const response = await proxy.$http.post('/user/create', userData)
        console.log('创建用户成功:', response)
        getUserList() // 重新获取列表
      } catch (error) {
        console.error('创建用户失败:', error)
      }
    }
    
    onMounted(() => {
      getUserList()
    })
    
    return {
      userList,
      loading,
      getUserList,
      createUser
    }
  }
}

3. 在独立的JavaScript文件中使用:
import http from '@/utils/http'

// 创建API服务类
class UserService {
  // 获取用户列表
  static async getUserList(params) {
    return await http.get('/user/list', params)
  }
  
  // 创建用户
  static async createUser(userData) {
    return await http.post('/user/create', userData)
  }
  
  // 更新用户
  static async updateUser(userId, userData) {
    return await http.put(`/user/update/${userId}`, userData)
  }
  
  // 删除用户
  static async deleteUser(userId) {
    return await http.delete(`/user/delete/${userId}`)
  }
}

export default UserService
*/