<template>
  <div class="nav-menu-container">
    <!-- 一级菜单 -->
    <ul class="level-one-menu">
      <li
        v-for="item in menuData"
        :key="item.name"
        :class="['menu-item', { 
          'active': item.name === activeTopMenu,
          'has-children': item.children && item.children.length > 0
        }]"
        :style="item.style"
        @click="handleTopMenuClick(item)"
      >
        <!-- 您可以替换成自己的图标 -->
        <!-- <span class="icon-placeholder"></span>  -->
        <el-icon color="#409efc" class="no-inherit">
            <Share />
        </el-icon>
        <span>{{ item.name }}</span>
      </li>
    </ul>

    <!-- 二级菜单 -->
    <ul class="level-two-menu" v-if="activeSubMenu.length > 0 && showSubMenu">
      <li
        v-for="subItem in activeSubMenu"
        :key="subItem.name"
        :class="['menu-item', { 'active': subItem.name === activeSubMenuItem }]"
        @click="handleSubMenuClick(subItem)"
      >
        <span>{{ subItem.name }}</span>
      </li>
    </ul>
  </div>
</template>

<script setup>
import { ref, computed,onMounted, onBeforeUnmount, getCurrentInstance } from 'vue';
import { useRouter } from 'vue-router';

const router = useRouter();

const activeTopMenu = ref('实时站场');
const activeSubMenuItem = ref('主系站场图');
const showSubMenu = ref(true);

const menuData = ref([
  {
    name: '实时站场',
    path: 'station',
    children: [
      { name: '主系站场图' },
      { name: 'I系站场图' },
      { name: 'II系站场图' },
      { name: 'I/II系站场图' },
    ]
  },
  {
    name: '驱动采集',
    path: 'driveGat',
  },
  {
    name: '网络状态',
    path: 'networkStatus',
    style: { background: '#D60000', color: 'white' }
  },
  {
    name: '安全接口',
    path: 'safeInterface',
    children: [
      { name: '安全接口1' },
      { name: '安全接口2' },
    ]
  },
  {
    name: '报警诊断',
    path: 'alarmDiagnosis',
    style: { background: '#D60000', color: 'white' }
  },
  {
    name: '信息查询',
    path: 'infoQuery',
  },
  {
    name: '历史回放',
    path: 'historyPlayback',
    style: { background: '#E6A23C', color: 'white' }
  },
  {
    name: '辅助功能',
    path: 'auxiliaryFunction',
  }
]);

const activeSubMenu = computed(() => {
  const activeItem = menuData.value.find(item => item.name === activeTopMenu.value);
  return activeItem && activeItem.children ? activeItem.children : [];
});

const handleTopMenuClick = (item) => {
  router.push({name: item.path});
  activeTopMenu.value = item.name;
  // 如果点击的一级菜单有子菜单，默认选中第一个子菜单
  if (item.children && item.children.length > 0) {
    activeSubMenuItem.value = item.children[0].name;
    showSubMenu.value = true;
  } else {
    activeSubMenuItem.value = null;
  }
  // 在这里可以添加路由跳转等逻辑
  // router.push(...)
  console.log('Clicked top menu:', item.name);
};

const handleSubMenuClick = (subItem) => {
  activeSubMenuItem.value = subItem.name;
  showSubMenu.value = false;
  // 在这里可以添加路由跳转等逻辑
  // router.push(...)
  //通过匹配子路由的父路由，然后分发emit事件，达到控制页面组件的目的。
  console.log('Clicked sub menu:', subItem.name);
};
</script>

<style scoped lang="scss">
.nav-menu-container {
  background-color: var(--color-night-blue);
  font-family: 'Microsoft YaHei', sans-serif;
  font-size: var(--menu-font-size);

  ul {
    list-style: none;
    padding: 0;
    margin: 0;
    display: flex;
    height: var(--header-height);
    border-bottom: 1px solid var(--color-border);
  }

  .level-two-menu {
    background-color: var(--color-night-blue);
  }

  .menu-item {
    min-width: var(--menu-width);
    cursor: pointer;
    color: var(--text-color);
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid #fff;
    white-space: nowrap;
    padding: 0 20px;
    box-sizing: border-box;
    &:hover {
      background-color: #000088;
    }

    &.active {
      background-color: var(--color-active-blue);
      color: var(--text-color);
    }
    .no-inherit {
        padding-right: 6px;
    }
  }

  .icon-placeholder {
    width: 16px;
    height: 16px;
    margin-right: 8px;
  }
}
</style> 