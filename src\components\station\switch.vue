<template>
  <svg attr="道岔">
    <g v-for="item in processedData" :key="item.usIndex + 'switch'">
      <!-- lineConfig -->
      <g v-for="(line, lineIndex) in item.lineConfig" :key="'line' + lineIndex">
        <line v-for="(lineItem, lineItemIndex) in line" :key="'line' + lineIndex + '-' + lineItemIndex"
          :class="[line1Style, 'flash-active']" :x1="lineItem.x1" :y1="lineItem.y1" :x2="lineItem.x2"
          :style="getLineStyle(lineItem)"
          :y2="lineItem.y2" />
      </g>
      <!-- lineConfig2 -->
      <g v-for="(line, lineIndex) in item.lineConfig2" :key="'line2' + lineIndex">
        <line v-for="(lineItem, lineItemIndex) in line" :key="'line2' + lineIndex + '-' + lineItemIndex" :class="line2Style"
          :x1="lineItem.x1" :y1="lineItem.y1" :x2="lineItem.x2" :y2="lineItem.y2" />
      </g>
      <!-- circleConfig, 静态配置给了坐标，是否显示由动态数据决定，目前暂时用isShow来控制 -->
      <g v-for="(circleItem, circleIndex) in item.circleConfig" :key="'circle' + circleIndex">
        <circle v-if="circleItem.isShow" :key="'circle' + circleIndex" :class="circleStyle" :cx="circleItem.x"
          :cy="circleItem.y" />
      </g>

      <!-- 使用 foreignObject 优雅地实现文本和边框 -->
      <foreignObject v-if="item.text" :x="item.textX" :y="item.textY" width="1" height="1" style="overflow: visible;">
        <div class="text-wrapper" :class="[textStyle, { 'has-red-border': item.bRedRect }]"
          :style="getTextStyle(item.textConfig[0])">
          {{ item.text }}
        </div>
      </foreignObject>
    </g>
  </svg>
</template>

<script setup>
import { computed, onMounted, ref } from 'vue'

const props = defineProps({
  data: {
    type: Array,
    required: true
  },
  theme: {
    type: Object,
    required: true
  },
})

const { line1Style, line2Style, textStyle, circleStyle, redRectStyle } = props.theme

// 使用 computed 缓存处理后的数据，避免重复计算
const processedData = computed(() => {
  return props.data.map(item => {
    const textConfig = item.textConfig?.[0]
    return {
      ...item, // 保留原始数据
      text: textConfig?.text || '',
      textX: textConfig?.x || 0,
      textY: textConfig?.y || 0
    }
  })
})

const getTextStyle = (textConfig) => {
  if (!textConfig) return {};
  return {
    color: `rgb(${textConfig.cClr})`
  }
}

// 给了fillColor，strokeColor如果没有给，则用fillColor；如果给了strokeColor，没有给fillColor,fillColor为none
const getLineStyle = (lineItem) => {
  const { cClrFlash, cClr } = lineItem
  if(cClrFlash && cClr) {
    if(flashFlag.value) {
      return {
        stroke: `rgb(${lineItem.cClrFlash})`
      }
    } else {
      return {
        stroke: `rgb(${lineItem.cClr})`
      }
    }
  } else if(cClr) {
    return {
      stroke: `rgb(${cClr})`
    }
  } else {
    return {}
  }
}

const flashFlag = ref(false)
const flashway = (flag) => {
  flashFlag.value = flag
}

defineExpose({
  flashway
})

</script>

<style lang="scss" scoped>
.text-wrapper {
  display: inline-block;
  white-space: nowrap;
  line-height: 1;
  font-size: 10px; // 定义字体大小以进行精确对齐

  // 通过 transform 将div向上平移，使其文本基线与 <foreignObject> 的 y 坐标对齐
  transform: translateY(-0.85em);

  padding: 0px 0px;
  border: 1px solid transparent; // 预留边框空间，避免有边框时布局晃动
}

.has-red-border {
  // redRectStyle 是一个class, 这里我们用CSS实现类似效果
  // 理想情况下，颜色应由主题CSS变量提供
  border-color: red;
}

.text-style-2 {
  color: rgb(255,0,0);
}
</style>