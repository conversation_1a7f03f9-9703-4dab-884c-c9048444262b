<script setup>
import { computed } from 'vue'

const props = defineProps({
  theme: {
    type: Object,
    required: true
  },
  data: {
    type: Array,
    required: true
  },
})

const { lineStyle } = props.theme

const processedData = computed(() => {
  const elements = []
  props.data.forEach((item, itemIndex) => {
    if(item.lineConfig) {
      item.lineConfig.forEach((line, lineIndex) => {
        elements.push({
          id: `box-rect-${itemIndex}-${lineIndex}`,
          type: 'line',
          x1: line.x1,
          y1: line.y1,
          x2: line.x2,
          y2: line.y2,
        })
      })
    }
  })
  return elements
})
</script>

<template>
  <g attr="箱体">
    <template v-for="element in processedData" :key="element.id">
        <line :class="lineStyle" :x1="element.x1" :y1="element.y1" :x2="element.x2" :y2="element.y2" />
    </template>
  </g>
</template>

<style scoped></style>