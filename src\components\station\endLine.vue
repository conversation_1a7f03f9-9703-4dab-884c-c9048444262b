<template>
    <g attr="尽头线">
        <template v-for="element in endLineData" :key="element.id">
            <line :class="lineStyle" :x1="element.x1" :y1="element.y1" :x2="element.x2" :y2="element.y2" />
        </template>
    </g>
</template>

<script setup>
import { computed } from 'vue';

const props = defineProps({
    theme: {
        type: Object,
        required: true
    },
    data: {
        type: Array,
        required: true
    }
})

const { lineStyle } = props.theme

const endLineData = computed(() => {
    const elements = []
    props.data.forEach((item, itemIndex) => {
        if(item.lineConfig) {
            item.lineConfig.forEach((line, lineIndex) => {
                elements.push(
                    {
                        id: `endLine-${itemIndex}-${lineIndex}`,
                        type: 'line',
                        ...line
                    }
                )
            })
        }
    })
    return elements
})
</script>

<style lang="scss" scoped>

</style>