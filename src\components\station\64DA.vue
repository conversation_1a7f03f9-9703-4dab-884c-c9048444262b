<template>
  <g attr="64DA">
    <!-- 使用单个循环渲染所有预处理后的元素 -->
    <template v-for="element in processedElements" :attr="element.id" :key="element.id">
      <!-- 箭头元素 -->
      <polygon 
        v-if="element.type === 'arrow'" 
        :class="arrowStyle" 
        :points="element.points" 
      />
      <!-- 文本元素 -->
      <text 
        v-else-if="element.type === 'text'" 
        :class="textStyle" 
        :x="element.x" 
        :y="element.y"
      >
        {{ element.text }}
      </text>
      <!-- 圆形元素 -->
      <circle 
        v-else-if="element.type === 'circle'" 
        :class="element.className" 
        :cx="element.x" 
        :cy="element.y"
      />
    </template>
  </g>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  data: {
    type: Array,
    required: true
  },
  theme: {
    type: Object,
    required: true
  },
})

const { arrowStyle, textStyle, circleStyle } = props.theme

// 计算属性：预处理所有元素，减少渲染时的计算
const processedElements = computed(() => {
  const elements = []
  
  props.data.forEach((item, itemIndex) => {
    // 处理箭头
    if (item.arrowConfig) {
      item.arrowConfig.forEach((arrow, arrowIndex) => {
        elements.push({
          id: `arrow-${itemIndex}-${arrowIndex}`,
          type: 'arrow',
          points: getArrowPointsCached(arrow)
        })
      })
    }
    
    // 处理文本
    if (item.textConfig) {
      item.textConfig.forEach((text, textIndex) => {
        elements.push({
          id: `text-${itemIndex}-${textIndex}`,
          type: 'text',
          x: text.x,
          y: text.y,
          text: text.text
        })
      })
    }
    
    // 处理圆形
    if (item.circleConfig) {
      item.circleConfig.forEach((circle, circleIndex) => {
        elements.push({
          id: `circle-${itemIndex}-${circleIndex}`,
          type: 'circle',
          x: circle.x,
          y: circle.y,
          className: getCircleClass(circle)
        })
      })
    }
  })
  
  return elements
})


// 优化后的箭头点计算
const getArrowPointsCached = (arrow) => {
  // 使用对象引用作为缓存键，避免字符串拼接
  if (arrow._cachedPoints) {
    return arrow._cachedPoints
  }
  
  const { x, y, dir } = arrow
  let points
  
  if (dir === 1) {
    points = `${x},${y-3} ${x-12},${y-3} ${x-12},${y-7} ${x-19},${y} ${x-12},${y+7} ${x-12},${y+3} ${x},${y+3}`
  } else if (dir === 2) {
    points = `${x},${y-3} ${x+12},${y-3} ${x+12},${y-7} ${x+19},${y} ${x+12},${y+7} ${x+12},${y+3} ${x},${y+3}`
  }
  
  // 缓存到原始对象上，避免额外的Map
  arrow._cachedPoints = points
  return points
}

// 优化后的类名处理
const getCircleClass = (circle) => {
  return circle.style ? `circle-style-${circle.style}` : circleStyle
}
</script>

<style scoped>
/* 可以添加一些过渡动画优化 */
</style>