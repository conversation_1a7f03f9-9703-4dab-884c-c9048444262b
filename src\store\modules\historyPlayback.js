// HistoryPlayback 模块状态管理
const state = {
  // 回放控制状态
  replayTarget: 'station',
  isPaused: true,
  currentTime: '2025/05/27 09:43:08.853',
  progress: 50,
  speed: 1,
  
  // 回放选项
  replayTargetOptions: [
    { value: 'station', label: '回放站场' },
    { value: 'network', label: '回放网络' },
    { value: 'drive', label: '回放驱动采集' },
    { value: 'security', label: '回放安全接口' }
  ],
  
  speedOptions: [
    { value: 1, label: '1' },
    { value: 2, label: '2' },
    { value: 3, label: '3' },
    { value: 4, label: '4' },
    { value: 5, label: '5' }
  ],
  
  // 搜索和筛选条件
  searchConditions: {
    startDate: '',
    endDate: '',
    keyword: '',
    eventType: '',
    deviceId: ''
  },
  
  // 回放数据
  replayData: [],
  totalDuration: 0,
  
  // 用户偏好设置
  userPreferences: {
    autoPlay: false,
    defaultSpeed: 1,
    showMilliseconds: true,
    defaultTarget: 'station'
  }
}

const mutations = {
  // 回放控制相关
  SET_REPLAY_TARGET(state, target) {
    state.replayTarget = target
  },
  
  SET_IS_PAUSED(state, isPaused) {
    state.isPaused = isPaused
  },
  
  SET_CURRENT_TIME(state, time) {
    state.currentTime = time
  },
  
  SET_PROGRESS(state, progress) {
    state.progress = progress
  },
  
  SET_SPEED(state, speed) {
    state.speed = speed
  },
  
  SET_TOTAL_DURATION(state, duration) {
    state.totalDuration = duration
  },
  
  // 搜索条件相关
  SET_SEARCH_CONDITIONS(state, conditions) {
    state.searchConditions = { ...state.searchConditions, ...conditions }
  },
  
  RESET_SEARCH_CONDITIONS(state) {
    state.searchConditions = {
      startDate: '',
      endDate: '',
      keyword: '',
      eventType: '',
      deviceId: ''
    }
  },
  
  // 回放数据相关
  SET_REPLAY_DATA(state, data) {
    state.replayData = data
  },
  
  ADD_REPLAY_DATA(state, data) {
    state.replayData.push(...data)
  },
  
  CLEAR_REPLAY_DATA(state) {
    state.replayData = []
  },
  
  // 用户偏好设置
  SET_USER_PREFERENCES(state, preferences) {
    state.userPreferences = { ...state.userPreferences, ...preferences }
  }
}

const actions = {
  // 切换播放/暂停
  togglePlay({ commit, state }) {
    commit('SET_IS_PAUSED', !state.isPaused)
    // 保存播放状态到本地存储
    localStorage.setItem('historyPlayback_isPaused', JSON.stringify(!state.isPaused))
  },
  
  // 设置回放目标
  setReplayTarget({ commit }, target) {
    commit('SET_REPLAY_TARGET', target)
    localStorage.setItem('historyPlayback_replayTarget', target)
  },
  
  // 设置播放速度
  setSpeed({ commit }, speed) {
    commit('SET_SPEED', speed)
    localStorage.setItem('historyPlayback_speed', speed.toString())
  },
  
  // 更新进度
  updateProgress({ commit }, progress) {
    commit('SET_PROGRESS', progress)
  },
  
  // 更新当前时间
  updateCurrentTime({ commit }, time) {
    commit('SET_CURRENT_TIME', time)
  },
  
  // 保存搜索条件
  saveSearchConditions({ commit }, conditions) {
    commit('SET_SEARCH_CONDITIONS', conditions)
    localStorage.setItem('historyPlayback_searchConditions', JSON.stringify(conditions))
  },
  
  // 重置回放状态
  resetPlayback({ commit }) {
    commit('SET_IS_PAUSED', true)
    commit('SET_PROGRESS', 0)
    commit('SET_CURRENT_TIME', '')
    commit('CLEAR_REPLAY_DATA')
  },
  
  // 保存用户偏好
  saveUserPreferences({ commit }, preferences) {
    commit('SET_USER_PREFERENCES', preferences)
    localStorage.setItem('historyPlayback_userPreferences', JSON.stringify(preferences))
  },
  
  // 从本地存储恢复状态
  restoreFromStorage({ commit }) {
    try {
      const replayTarget = localStorage.getItem('historyPlayback_replayTarget')
      const isPaused = localStorage.getItem('historyPlayback_isPaused')
      const speed = localStorage.getItem('historyPlayback_speed')
      const searchConditions = localStorage.getItem('historyPlayback_searchConditions')
      const userPreferences = localStorage.getItem('historyPlayback_userPreferences')
      
      if (replayTarget) commit('SET_REPLAY_TARGET', replayTarget)
      if (isPaused !== null) commit('SET_IS_PAUSED', JSON.parse(isPaused))
      if (speed) commit('SET_SPEED', parseInt(speed))
      if (searchConditions) commit('SET_SEARCH_CONDITIONS', JSON.parse(searchConditions))
      if (userPreferences) commit('SET_USER_PREFERENCES', JSON.parse(userPreferences))
    } catch (error) {
      console.warn('恢复历史回放状态失败:', error)
    }
  },
  
  // 加载回放数据
  async loadReplayData({ commit }, { startTime, endTime, target }) {
    try {
      // 这里应该调用API获取数据
      // const response = await api.getReplayData({ startTime, endTime, target })
      // commit('SET_REPLAY_DATA', response.data)
      
      // 临时模拟数据
      const mockData = [
        { time: startTime, event: '开始回放', target },
        { time: endTime, event: '结束回放', target }
      ]
      commit('SET_REPLAY_DATA', mockData)
    } catch (error) {
      console.error('加载回放数据失败:', error)
    }
  }
}

const getters = {
  // 获取当前回放状态摘要
  playbackSummary: (state) => ({
    target: state.replayTarget,
    isPlaying: !state.isPaused,
    currentTime: state.currentTime,
    progress: state.progress,
    speed: state.speed
  }),
  
  // 获取格式化的当前时间
  formattedCurrentTime: (state) => {
    if (!state.userPreferences.showMilliseconds && state.currentTime) {
      return state.currentTime.replace(/\.\d{3}$/, '')
    }
    return state.currentTime
  },
  
  // 检查是否有活跃的搜索条件
  hasActiveSearch: (state) => {
    const conditions = state.searchConditions
    return !!(conditions.startDate || conditions.endDate || conditions.keyword || 
             conditions.eventType || conditions.deviceId)
  },
  
  // 获取当前选中目标的显示标签
  currentTargetLabel: (state) => {
    const option = state.replayTargetOptions.find(opt => opt.value === state.replayTarget)
    return option ? option.label : state.replayTarget
  },
  
  // 计算回放进度百分比
  progressPercentage: (state) => {
    return `${state.progress}%`
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters
} 