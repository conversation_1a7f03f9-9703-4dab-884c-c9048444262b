<template>
    <g attr="坡道">
        <template v-for="(item, index) in rampData" :key="'ramp'+index">
            <text v-if="item.type === 'text'" :class="textStyle" :x="item.x" :y="item.y">
                {{ item.text }}
            </text>
            <line v-if="item.type === 'line'" :class="lineStyle" :x1="item.x1" :y1="item.y1" :x2="item.x2" :y2="item.y2" />
        </template>
    </g>
</template>

<script setup>
import { computed } from 'vue';

const props = defineProps({
    theme: {
        type: Object,
        required: true
    },
    data: {
        type: Array,
        required: true
    }
})

const { textStyle, lineStyle } = props.theme

const rampData = computed(() => {
    const element = []
    props.data.forEach(item => {
        if(item.textConfig) {
            item.textConfig.forEach(text => {
                element.push({
                    type: 'text',
                    ...text,
                })
            })
        }
        if(item.lineConfig) {
            item.lineConfig.forEach(line => {
                element.push({
                    type: 'line',
                    ...line,
                })
            })
        }
    })
    return element
})
</script>

<style scoped>
</style>