<template>
  <div class="history-playback">
    <!-- 这里可以添加搜索条件表单 -->
    <div class="search-section" v-if="showSearchPanel">
      <el-card>
        <template #header>
          <span>搜索条件</span>
          <el-button 
            style="float: right; padding: 3px 0" 
            type="text" 
            @click="toggleSearchPanel"
          >
            收起
          </el-button>
        </template>
        
        <el-form :model="searchForm" label-width="100px">
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="开始日期:">
                <el-date-picker
                  v-model="searchForm.startDate"
                  type="datetime"
                  placeholder="选择开始时间"
                  format="YYYY-MM-DD HH:mm:ss"
                  value-format="YYYY-MM-DD HH:mm:ss"
                  @change="saveSearchConditions"
                />
              </el-form-item>
            </el-col>
            
            <el-col :span="8">
              <el-form-item label="结束日期:">
                <el-date-picker
                  v-model="searchForm.endDate"
                  type="datetime"
                  placeholder="选择结束时间"
                  format="YYYY-MM-DD HH:mm:ss"
                  value-format="YYYY-MM-DD HH:mm:ss"
                  @change="saveSearchConditions"
                />
              </el-form-item>
            </el-col>
            
            <el-col :span="8">
              <el-form-item label="关键词:">
                <el-input
                  v-model="searchForm.keyword"
                  placeholder="请输入搜索关键词"
                  @input="saveSearchConditions"
                />
              </el-form-item>
            </el-col>
          </el-row>
          
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="事件类型:">
                <el-select
                  v-model="searchForm.eventType"
                  placeholder="选择事件类型"
                  @change="saveSearchConditions"
                >
                  <el-option label="全部" value="" />
                  <el-option label="设备故障" value="fault" />
                  <el-option label="状态变化" value="status" />
                  <el-option label="操作记录" value="operation" />
                </el-select>
              </el-form-item>
            </el-col>
            
            <el-col :span="8">
              <el-form-item label="设备ID:">
                <el-input
                  v-model="searchForm.deviceId"
                  placeholder="请输入设备ID"
                  @input="saveSearchConditions"
                />
              </el-form-item>
            </el-col>
            
            <el-col :span="8">
              <el-form-item>
                <el-button type="primary" @click="handleSearch">搜索</el-button>
                <el-button @click="resetSearch">重置</el-button>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </el-card>
    </div>
    
    <!-- 折叠的搜索按钮 -->
    <div class="collapsed-search" v-else>
      <el-button type="primary" @click="toggleSearchPanel">
        展开搜索条件
      </el-button>
      <span class="search-summary" v-if="hasActiveSearch">
        当前搜索: {{ searchSummary }}
      </span>
    </div>
    
    <!-- 回放工具组件 -->
    <ReplayTool />
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useStore } from 'vuex'
import ReplayTool from '@/components/replayTool.vue'

const store = useStore()

// 本地状态
const showSearchPanel = ref(true)

// 搜索表单数据
const searchForm = computed({
  get: () => store.state.historyPlayback.searchConditions,
  set: (value) => store.commit('historyPlayback/SET_SEARCH_CONDITIONS', value)
})

// 计算属性
const hasActiveSearch = computed(() => store.getters['historyPlayback/hasActiveSearch'])
const searchSummary = computed(() => {
  const conditions = store.state.historyPlayback.searchConditions
  const summary = []
  if (conditions.startDate) summary.push(`开始: ${conditions.startDate}`)
  if (conditions.endDate) summary.push(`结束: ${conditions.endDate}`)
  if (conditions.keyword) summary.push(`关键词: ${conditions.keyword}`)
  if (conditions.eventType) summary.push(`类型: ${conditions.eventType}`)
  if (conditions.deviceId) summary.push(`设备: ${conditions.deviceId}`)
  return summary.join(', ')
})

// 方法
const toggleSearchPanel = () => {
  showSearchPanel.value = !showSearchPanel.value
}

const saveSearchConditions = () => {
  // 使用防抖延迟保存
  clearTimeout(saveSearchConditions.timer)
  saveSearchConditions.timer = setTimeout(() => {
    store.dispatch('historyPlayback/saveSearchConditions', searchForm.value)
  }, 500)
}

const handleSearch = () => {
  console.log('执行搜索:', searchForm.value)
  // 这里可以调用API进行搜索
  store.dispatch('historyPlayback/loadReplayData', {
    startTime: searchForm.value.startDate,
    endTime: searchForm.value.endDate,
    target: store.state.historyPlayback.replayTarget
  })
}

const resetSearch = () => {
  store.commit('historyPlayback/RESET_SEARCH_CONDITIONS')
  console.log('搜索条件已重置')
}

const getFormattedCurrentTime = () => {
  const now = new Date()
  const year = now.getFullYear()
  const month = (now.getMonth() + 1).toString().padStart(2, '0')
  const day = now.getDate().toString().padStart(2, '0')
  const hours = now.getHours().toString().padStart(2, '0')
  const minutes = now.getMinutes().toString().padStart(2, '0')
  const seconds = now.getSeconds().toString().padStart(2, '0')
  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
}

// 生命周期
onMounted(async () => {
  // 恢复保存的状态
  await store.dispatch('historyPlayback/restoreFromStorage')

  const conditions = { ...store.state.historyPlayback.searchConditions }
  let needsUpdate = false

  if (!conditions.startDate) {
    conditions.startDate = getFormattedCurrentTime()
    needsUpdate = true
  }
  if (!conditions.endDate) {
    conditions.endDate = getFormattedCurrentTime()
    needsUpdate = true
  }
  if (conditions.eventType === undefined || conditions.eventType === null) {
    conditions.eventType = 'fault'
    needsUpdate = true
  }

  if (needsUpdate) {
    store.commit('historyPlayback/SET_SEARCH_CONDITIONS', conditions)
  }
  
  console.log('HistoryPlayback页面已挂载，状态已恢复')
})
</script>

<style lang="scss" scoped>
.history-playback {
  height: 90vh;
  display: flex;
  flex-direction: column;
  position: relative;
}

.search-section {
  padding: 20px;
  background: #f5f5f5;
  
  .el-card {
    margin-bottom: 10px;
  }
}

.collapsed-search {
  padding: 10px 20px;
  background: #f5f5f5;
  display: flex;
  align-items: center;
  gap: 15px;
  
  .search-summary {
    font-size: 12px;
    color: #666;
    background: #fff;
    padding: 5px 10px;
    border-radius: 4px;
    border: 1px solid #ddd;
  }
}
</style>