<template>
  <svg attr="区段">
    <g v-for="item in processedData" :key="item.usIndex">
      <line :class="[lineStyle, 'flash-active']" :x1="item.x1" :y1="item.y1" :x2="item.x2"
        :y2="item.y2" />
      <text :class="textStyle" :x="item.textX" :y="item.textY"> {{ item.text }} </text>
    </g>
  </svg>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  data: {
    type: Array,
    required: true
  },
  theme: {
    type: Object,
    required: true
  },
})

const { lineStyle, textStyle } = props.theme

// 使用 computed 缓存处理后的数据，避免重复计算
const processedData = computed(() => {
  return props.data.map(item => {
    const lineObj = item.lineConfig?.[0]
    const textObj = item.textConfig?.[0]
    
    return {
      ...item, // 保留原始数据
      x1: lineObj?.x1,
      y1: lineObj?.y1,
      x2: lineObj?.x2,
      y2: lineObj?.y2,
      text: textObj?.text,
      textX: textObj?.x,
      textY: textObj?.y
    }
  })
})
</script>

<style lang="scss" scoped></style>