<template>
    <div
        ref="switchSubRef"
        class="switch-sub"
        :class="{ 'dragging': isDragging }"
        :style="{
            left: position.x + 'px',
            top: position.y + 'px'
        }"
    >
        <div class="switch-sub-item">
            <div
                class="switch-sub-item-title"
                @mousedown="startDrag"
                :style="{ cursor: isDragging ? 'grabbing' : 'grab' }"
            >
                道岔分表示
                <span class="switch-sub-item-title-close" @click="closeSwitchSub">
                    <el-icon><Close /></el-icon>
                </span>
            </div>
            <div class="switch-sub-item-name">
                <span class="dcname">道岔名称</span>
                <span class="jname" v-for="item in JNumber" :attr="item+'j'" :key="item+'j'">尖{{ item }}</span>
                <span class="xname" v-for="item in XNumber" :key="item+'x'">心{{ item }}</span>
            </div>
            <div class="switch-sub-item-content-box">
                <div class="switch-sub-item-content" v-for="item in switchSubData" :key="item.switchName+'switchName'">
                    <span class="dcname">{{ item.switchName }}</span>
                    <span v-for="jItem in item.jArr" :key="jItem.isShow+'jItem'">
                        <i :class="jItem.isShow ? 'circle' : ''"></i>
                    </span>
                    <span v-for="xItem in item.xArr" :key="xItem.isShow+'xItem'">
                        <i :class="xItem.isShow ? 'circle' : ''"></i>
                    </span>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { computed, ref, onBeforeUnmount } from 'vue';
import { Close } from '@element-plus/icons-vue';

const emit = defineEmits(['closeSwitchSub']);

const props = defineProps({
    JNumber: {
        type: Number,
        default: 0
    },
    XNumber: {
        type: Number,
        default: 0
    },
    data: {
        type: Array,
        default: () => []
    }
});

// 拖拽相关状态
const switchSubRef = ref(null);
const isDragging = ref(false);
const position = ref({ x: 10, y: 10 }); // 初始位置，与CSS中的定位一致
const dragOffset = ref({ x: 0, y: 0 });
const parentBounds = ref(null);

// 开始拖拽
const startDrag = (event) => {
    // 防止事件冒泡和默认行为
    event.stopPropagation();
    event.preventDefault();

    // 获取父容器边界，用于限制拖拽范围
    if (switchSubRef.value && switchSubRef.value.parentElement) {
        const parentRect = switchSubRef.value.parentElement.getBoundingClientRect();
        parentBounds.value = {
            left: 0,
            top: 0,
            right: parentRect.width,
            bottom: parentRect.height,
            parentRect: parentRect // 保存父容器的屏幕坐标
        };
    }

    // 计算鼠标点击位置与组件当前位置的偏移量
    // 使用当前的 position 值而不是 getBoundingClientRect
    dragOffset.value = {
        x: event.clientX - (parentBounds.value?.parentRect.left || 0) - position.value.x,
        y: event.clientY - (parentBounds.value?.parentRect.top || 0) - position.value.y
    };

    // 设置拖拽状态
    isDragging.value = true;

    // 添加鼠标移动和释放事件监听
    document.addEventListener('mousemove', onDrag);
    document.addEventListener('mouseup', stopDrag);
};

// 拖拽过程
const onDrag = (event) => {
    if (!isDragging.value) return;

    // 计算新位置 - 转换为相对于父容器的坐标
    let newX = event.clientX - (parentBounds.value?.parentRect.left || 0) - dragOffset.value.x;
    let newY = event.clientY - (parentBounds.value?.parentRect.top || 0) - dragOffset.value.y;

    // 限制在父容器范围内
    if (parentBounds.value && switchSubRef.value) {
        const elementRect = switchSubRef.value.getBoundingClientRect();
        const elementWidth = elementRect.width;
        const elementHeight = elementRect.height;

        // 限制左右边界
        newX = Math.max(0, Math.min(newX, parentBounds.value.right - elementWidth));

        // 限制上下边界
        newY = Math.max(0, Math.min(newY, parentBounds.value.bottom - elementHeight));
    }

    // 更新位置
    position.value = { x: newX, y: newY };
};

// 停止拖拽
const stopDrag = () => {
    isDragging.value = false;
    document.removeEventListener('mousemove', onDrag);
    document.removeEventListener('mouseup', stopDrag);
};

// 组件数据处理
const switchSubData = computed(()=>{
    return props.data.map(item=>{
        let jArr = []
        let xArr = []
        const { JCfg, XCfg } = item
        for(let i = 0; i < props.JNumber; i++) {
            const jCfg = JCfg?.[i]
            if(jCfg) {
                jArr.push({ isShow: true })
            } else {
                jArr.push({})
            }
        }
        for(let i = 0; i < props.XNumber; i++) {
            const xCfg = XCfg?.[i]
            if(xCfg) {
                xArr.push({ isShow: true })
            } else {
                xArr.push({})
            }
        }
        return {
            switchName: item.switchIndex,
            jArr: jArr,
            xArr: xArr
        }
    })
});

// 关闭道岔分表示
const closeSwitchSub = () => {
    emit('closeSwitchSub');
};

// 组件卸载时清理事件监听
onBeforeUnmount(() => {
    document.removeEventListener('mousemove', onDrag);
    document.removeEventListener('mouseup', stopDrag);
});
</script>

<style lang="scss" scoped>
.switch-sub {
    position: absolute;
    width: auto;
    height: 133px;
    z-index: 1001;
    background-color: rgb(128,128,128);
    user-select: none; // 防止拖拽时选中文本

    &.dragging {
        z-index: 1002;
    }

    .switch-sub-item-title {
        width: 100%;
        height: 20px;
        line-height: 20px;
        font-size: 12px;
        text-align: center;
        color: #fff;
        background-color: rgb(60, 91, 169);
        position: relative;

        .switch-sub-item-title-close {
            position: absolute;
            top: 2px;
            right: 2px;
            cursor: pointer;
            border: 1px solid #fff;
            height: 14px;
            background-color: #fff;
            display: flex;
            align-items: center;
            z-index: 1003; // 确保关闭按钮在最上层

            i {
                color: #000;
            }
        }
    }
    .switch-sub-item-name{
        span {
            font-size: 12px;
            display: inline-block;
            min-width: 30px;
            height: 18px;
            line-height: 18px;
            color: #000;
            text-align: center;
            background-color: rgb(192, 192, 192);
            margin: 0.5px;
            &.dcname {
                width: 50px;
            }
        }
    }
    .switch-sub-item-content-box {
        width: 100%;
        height: 90px;
        background-color: rgb(192, 192, 192);
        overflow-y: auto;
    }
    .switch-sub-item-content {
        display: flex;
        span {
            font-size: 12px;
            display: inline-block;
            min-width: 30px;
            height: 18px;
            line-height: 18px;
            color: #fff;
            text-align: center;
            background-color: rgb(0, 0, 0);
            margin: 0.5px;
            display: flex;
            align-items: center;
            justify-content: center;
            .circle {
                display: inline-block;
                width: 10px;
                height: 10px;
                border-radius: 50%;
                border: 1px solid #fff;
            }
            &.dcname {
                width: 50px;
            }
        }
    }
}
</style>