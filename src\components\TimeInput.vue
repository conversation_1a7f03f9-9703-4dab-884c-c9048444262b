<template>
  <div class="time-input-container">
    <label v-if="label" :for="inputId" class="time-label">{{ label }}</label>
    <div class="input-with-arrows">
      <IMaskInput
        ref="imaskRef"
        :id="inputId"
        v-model="internalValue"
        :mask="currentMask.mask"
        :blocks="currentMask.blocks"
        :lazy="currentMask.lazy"
        :overwrite="currentMask.overwrite"
        :placeholder="placeholder"
        :disabled="disabled"
        @accept="onAccept"
        @complete="onComplete"
        @focus="onFocus"
        @blur="onBlur"
      />
      <div v-if="showArrows" class="arrow-buttons">
        <button 
          @click="adjustTime('up')" 
          class="arrow-btn"
          :disabled="disabled"
          type="button"
        >▲</button>
        <button 
          @click="adjustTime('down')" 
          class="arrow-btn"
          :disabled="disabled"
          type="button"
        >▼</button>
      </div>
    </div>
    
    <!-- 调试模式显示信息 -->
    <div v-if="debug" class="debug-info">
      <p>格式: {{ format }}</p>
      <p>当前值: {{ internalValue }}</p>
      <p>光标位置: {{ lastCursorPos }}</p>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted, nextTick } from 'vue';
import { IMaskComponent, IMask } from 'vue-imask';

// 注册组件
const IMaskInput = IMaskComponent;

// Props 定义
const props = defineProps({
  // v-model 绑定的值
  modelValue: {
    type: String,
    default: ''
  },
  // 时间格式类型: 'date' 或 'time'
  format: {
    type: String,
    default: 'time', // 'time' | 'date'
    validator: (value) => ['time', 'date'].includes(value)
  },
  // 标签文本
  label: {
    type: String,
    default: ''
  },
  // 是否显示上下箭头
  showArrows: {
    type: Boolean,
    default: true
  },
  // 是否禁用
  disabled: {
    type: Boolean,
    default: false
  },
  // 是否显示当前时间作为默认值
  showCurrentTime: {
    type: Boolean,
    default: true
  },
  // 自定义占位符
  customPlaceholder: {
    type: String,
    default: ''
  },
  // 调试模式
  debug: {
    type: Boolean,
    default: false
  }
});

// Emits 定义
const emit = defineEmits(['update:modelValue', 'change', 'focus', 'blur']);

// 响应式数据
const internalValue = ref('');
const imaskRef = ref(null);
const maskInstance = ref(null);
const lastCursorPos = ref(0);
const isFocused = ref(false);

// 生成唯一 ID
const inputId = `time-input-${Math.random().toString(36).substr(2, 9)}`;

// 计算属性：占位符
const placeholder = computed(() => {
  if (props.customPlaceholder) return props.customPlaceholder;
  return props.format === 'date' ? 'YYYY-MM-DD' : 'HH:mm:ss';
});

// 计算属性：当前掩码配置
const currentMask = computed(() => {
  // 共享的基础配置
  const baseOptions = {
    lazy: false,
    overwrite: true,
  };

  if (props.format === 'date') {
    return {
      ...baseOptions,
      mask: 'YYYY-MM-DD',
      blocks: {
        YYYY: {
          mask: IMask.MaskedRange,
          from: 1900,
          to: 2999,
          maxLength: 4,
          autofix: 'pad'
        },
        MM: {
          mask: IMask.MaskedRange,
          from: 1,
          to: 12,
          maxLength: 2,
          autofix: 'pad'
        },
        DD: {
          mask: IMask.MaskedRange,
          from: 1,
          to: 31,
          maxLength: 2,
          autofix: 'pad'
        }
      }
    };
  } else {
    return {
      ...baseOptions,
      mask: 'HH:mm:ss',
      blocks: {
        HH: {
          mask: IMask.MaskedRange,
          from: 0,
          to: 23,
          maxLength: 2,
          autofix: 'pad'
        },
        mm: {
          mask: IMask.MaskedRange,
          from: 0,
          to: 59,
          maxLength: 2,
          autofix: 'pad'
        },
        ss: {
          mask: IMask.MaskedRange,
          from: 0,
          to: 59,
          maxLength: 2,
          autofix: 'pad'
        }
      }
    };
  }
});

// 获取当前时间字符串
const getCurrentTimeString = () => {
  const now = new Date();
  
  if (props.format === 'date') {
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  } else {
    const hours = String(now.getHours()).padStart(2, '0');
    const minutes = String(now.getMinutes()).padStart(2, '0');
    const seconds = String(now.getSeconds()).padStart(2, '0');
    return `${hours}:${minutes}:${seconds}`;
  }
};

// 事件处理函数
const onAccept = (maskRef) => {
  maskInstance.value = maskRef;
  emit('update:modelValue', internalValue.value);
  emit('change', internalValue.value);
};

const onComplete = (maskRef) => {
  maskInstance.value = maskRef;
  if (props.debug) {
    console.log('时间输入完成:', maskRef.value);
  }
};

const onFocus = () => {
  isFocused.value = true;
  emit('focus');
};

const onBlur = () => {
  isFocused.value = false;
  emit('blur');
};

// 调整时间的核心函数
const adjustTime = async (direction) => {
  if (props.disabled) return;
  
  await nextTick();
  
  let mask = maskInstance.value;
  
  if (!mask && imaskRef.value) {
    mask = imaskRef.value.maskRef || imaskRef.value.mask || imaskRef.value.$imask;
  }
  
  if (!mask) {
    if (!internalValue.value && props.showCurrentTime) {
      internalValue.value = getCurrentTimeString();
    }
    return;
  }

  // 获取光标位置
  let cursorPos = 0;
  let inputElement = null;
  
  if (imaskRef.value) {
    inputElement = imaskRef.value.$el;
    if (!inputElement || inputElement.tagName !== 'INPUT') {
      inputElement = imaskRef.value.$el?.querySelector('input');
    }
    if (!inputElement) {
      inputElement = document.getElementById(inputId);
    }
  }
  
  if (inputElement && inputElement.tagName === 'INPUT') {
    cursorPos = inputElement.selectionStart || 0;
  } else {
    cursorPos = mask.cursorPos || 0;
  }
  
  lastCursorPos.value = cursorPos;
  
  // 获取当前值
  let currentValue = mask.value || internalValue.value || getCurrentTimeString();
  
  if (props.format === 'date') {
    adjustDate(currentValue, cursorPos, direction, mask, inputElement);
  } else {
    adjustTimeValue(currentValue, cursorPos, direction, mask, inputElement);
  }
};

// 调整日期
const adjustDate = (currentValue, cursorPos, direction, mask, inputElement) => {
  // 补全日期格式
  if (currentValue.length < 10) {
    const parts = currentValue.split('-');
    while (parts.length < 3) {
      parts.push('01');
    }
    currentValue = parts.map((part, index) => {
      if (index === 0) return part.padStart(4, '0'); // 年份
      return part.padStart(2, '0'); // 月份和日期
    }).join('-');
  }
  
  let [year, month, day] = currentValue.split('-').map(Number);
  
  // YYYY-MM-DD
  // 0123456789 (光标位置)
  if (cursorPos <= 4) { // 调整年份
    year = direction === 'up' ? 
      Math.min(year + 1, 2999) : 
      Math.max(year - 1, 1900);
  } else if (cursorPos <= 7) { // 调整月份
    month = direction === 'up' ? 
      (month % 12) + 1 : 
      month === 1 ? 12 : month - 1;
  } else { // 调整日期
    const daysInMonth = new Date(year, month, 0).getDate();
    day = direction === 'up' ? 
      (day % daysInMonth) + 1 : 
      day === 1 ? daysInMonth : day - 1;
  }
  
  const newValue = `${year}-${String(month).padStart(2, '0')}-${String(day).padStart(2, '0')}`;
  updateValue(newValue, mask, inputElement, cursorPos);
};

// 调整时间
const adjustTimeValue = (currentValue, cursorPos, direction, mask, inputElement) => {
  // 补全时间格式
  if (currentValue.length < 8) {
    const parts = currentValue.split(':');
    while (parts.length < 3) {
      parts.push('00');
    }
    currentValue = parts.map(part => part.padStart(2, '0')).join(':');
  }
  
  let [hours, minutes, seconds] = currentValue.split(':').map(Number);
  
  // HH:mm:ss
  // 01234567 (光标位置)
  if (cursorPos <= 2) { // 调整小时
    hours = (direction === 'up') ? (hours + 1) % 24 : (hours - 1 + 24) % 24;
  } else if (cursorPos <= 5) { // 调整分钟
    minutes = (direction === 'up') ? (minutes + 1) % 60 : (minutes - 1 + 60) % 60;
  } else { // 调整秒
    seconds = (direction === 'up') ? (seconds + 1) % 60 : (seconds - 1 + 60) % 60;
  }
  
  const newValue = `${String(hours).padStart(2, '0')}:${String(minutes).padStart(2, '0')}:${String(seconds).padStart(2, '0')}`;
  updateValue(newValue, mask, inputElement, cursorPos);
};

// 更新值并恢复光标位置
const updateValue = async (newValue, mask, inputElement, cursorPos) => {
  if (mask && mask.value !== undefined) {
    mask.value = newValue;
  } else {
    internalValue.value = newValue;
  }
  
  await nextTick();
  
  if (inputElement && inputElement.tagName === 'INPUT') {
    inputElement.focus();
    inputElement.setSelectionRange(cursorPos, cursorPos);
  } else if (mask && mask.cursorPos !== undefined) {
    mask.cursorPos = cursorPos;
  }
};

// 暴露给父组件的方法
const focus = () => {
  if (imaskRef.value && imaskRef.value.$el) {
    const input = imaskRef.value.$el.tagName === 'INPUT' ? 
      imaskRef.value.$el : 
      imaskRef.value.$el.querySelector('input');
    if (input) {
      input.focus();
    }
  }
};

const setValue = (value) => {
  internalValue.value = value;
};

const getCurrentValue = () => {
  return internalValue.value;
};

// 监听 props.modelValue 变化
watch(() => props.modelValue, (newValue) => {
  if (newValue !== internalValue.value) {
    internalValue.value = newValue;
  }
}, { immediate: true });

// 监听 internalValue 变化
watch(internalValue, (newValue) => {
  if (newValue !== props.modelValue) {
    emit('update:modelValue', newValue);
  }
});

// 组件挂载时设置默认值
onMounted(() => {
  if (!props.modelValue && props.showCurrentTime) {
    internalValue.value = getCurrentTimeString();
  }
});

// 暴露方法给父组件
defineExpose({
  focus,
  setValue,
  getCurrentValue
});
</script>

<style scoped>
.time-input-container {
  display: inline-block;
  font-family: Arial, sans-serif;
}

.time-label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
  color: #333;
  font-size: 14px;
}

.input-with-arrows {
  position: relative;
  display: inline-flex;
  align-items: center;
}

/* 覆盖 input 默认样式 */
.input-with-arrows > :deep(input) {
  padding: 8px 12px;
  border-radius: 4px;
  border: 1px solid #dcdfe6;
  font-size: 14px;
  width: 160px;
  transition: border-color 0.2s;
  background: #fff;
}

.input-with-arrows > :deep(input:focus) {
  border-color: #409eff;
  outline: none;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
}

.input-with-arrows > :deep(input:disabled) {
  background-color: #f5f7fa;
  border-color: #e4e7ed;
  color: #c0c4cc;
  cursor: not-allowed;
}

.arrow-buttons {
  display: flex;
  flex-direction: column;
  margin-left: 8px;
}

.arrow-btn {
  border: 1px solid #dcdfe6;
  background: #fff;
  cursor: pointer;
  padding: 2px 8px;
  font-size: 10px;
  line-height: 1.2;
  border-radius: 3px;
  transition: all 0.2s;
  user-select: none;
}

.arrow-btn:first-child {
  margin-bottom: 2px;
}

.arrow-btn:hover:not(:disabled) {
  background: #ecf5ff;
  border-color: #b3d8ff;
  color: #409eff;
}

.arrow-btn:active:not(:disabled) {
  background: #409eff;
  border-color: #409eff;
  color: #fff;
}

.arrow-btn:disabled {
  background-color: #f5f7fa;
  border-color: #e4e7ed;
  color: #c0c4cc;
  cursor: not-allowed;
}

.debug-info {
  margin-top: 8px;
  padding: 8px;
  background: #f5f7fa;
  border-radius: 4px;
  font-size: 12px;
  color: #606266;
}

.debug-info p {
  margin: 2px 0;
}
</style> 