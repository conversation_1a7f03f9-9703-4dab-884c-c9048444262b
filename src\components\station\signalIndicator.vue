<script setup>
import { computed, onMounted } from 'vue'

const props = defineProps({
  theme: {
    type: Object,
    required: true
  },
  data: {
    type: Array,
    required: true
  },
})

const processedData = computed(() => {
  const elements = []
  props.data.forEach((item, itemIndex) => {
    if(item.circleConfig) {
      item.circleConfig.forEach((circle, circleIndex) => {
        elements.push({
          id: `signalIndicator-circle-${itemIndex}-${circleIndex}`,
          type: 'circle',
          ...circle
        })
      })
    }
    if(item.lineConfig) {
      item.lineConfig.forEach((line, lineIndex) => {
        elements.push({
          id: `signalIndicator-line-${itemIndex}-${lineIndex}`,
          type: 'line',
          x1: line.x1,
          y1: line.y1,
          x2: line.x2,
          y2: line.y2,
        })
      })
    }
    if(item.textConfig) {
      item.textConfig.forEach((text, textIndex) => {
        elements.push({
          id: `signalIndicator-text-${itemIndex}-${textIndex}`,
          type: 'text',
          x: text.x,
          y: text.y,
          text: text.text,
        })
      })
    }
  })
  return elements
})

const { circleStyle, lineStyle, textStyle } = props.theme

const getCircleStyle = (element) => {
  return {
    fill: element.cClrFill?`rgb(${element.cClrFill})`:'',
    stroke: element.cClrStroke?`rgb(${element.cClrStroke})`:element.cClrFill?`rgb(${element.cClrFill})`:''
  }
}

</script>

<template>
  <g attr="信号指示灯">
    <template v-for="element in processedData" :key="element.id">
      <circle v-if="element.type === 'circle'" :class="circleStyle" :style="getCircleStyle(element)" :cx="element.x" :cy="element.y" />
      <line v-else-if="element.type === 'line'" :class="lineStyle" :x1="element.x1" :y1="element.y1" :x2="element.x2" :y2="element.y2" />
      <text v-else-if="element.type === 'text'" :class="textStyle" :x="element.x" :y="element.y">
        {{ element.text }}
      </text>
    </template>
  </g>
</template>

<style scoped></style>