/**
 * WebSocket管理模块
 * 负责WebSocket连接管理、消息分发、订阅管理等核心功能
 */

const state = {
  // WebSocket连接实例
  ws: null,
  // 连接状态
  isConnected: false,
  // 当前订阅的topic
  currentTopic: null,
  // 重连次数
  reconnectCount: 0,
  // 最大重连次数
  maxReconnectCount: 5,
  // 重连间隔(毫秒)
  reconnectInterval: 3000,
  // 心跳定时器
  heartbeatTimer: null,
  // 心跳间隔(毫秒)
  heartbeatInterval: 30000,
  // WebSocket服务器地址
  wsUrl: 'ws://192.168.8.93:2025/ws/cbi',
  // 接收到的消息数据
  messageData: {},
  // 错误信息
  error: null
}

const mutations = {
  // 设置WebSocket连接实例
  SET_WEBSOCKET(state, ws) {
    state.ws = ws
  },
  
  // 设置连接状态
  SET_CONNECTION_STATUS(state, status) {
    state.isConnected = status
  },
  
  // 设置当前订阅的topic
  SET_CURRENT_TOPIC(state, topic) {
    state.currentTopic = topic
  },
  
  // 设置重连次数
  SET_RECONNECT_COUNT(state, count) {
    state.reconnectCount = count
  },
  
  // 设置心跳定时器
  SET_HEARTBEAT_TIMER(state, timer) {
    state.heartbeatTimer = timer
  },
  
  // 设置消息数据
  SET_MESSAGE_DATA(state, { topic, data }) {
    state.messageData = {
      ...state.messageData,
      [topic]: data
    }
  },
  
  // 清除消息数据
  CLEAR_MESSAGE_DATA(state, topic) {
    if (topic) {
      delete state.messageData[topic]
    } else {
      state.messageData = {}
    }
  },
  
  // 设置错误信息
  SET_ERROR(state, error) {
    state.error = error
  },
  
  // 清除错误信息
  CLEAR_ERROR(state) {
    state.error = null
  }
}

const actions = {
  // 初始化WebSocket连接
  initWebSocket({ commit, dispatch, state }) {
    return new Promise((resolve, reject) => {
      try {
        const ws = new WebSocket(state.wsUrl)
        
        ws.onopen = () => {
          console.log('WebSocket连接成功')
          commit('SET_WEBSOCKET', ws)
          commit('SET_CONNECTION_STATUS', true)
          commit('SET_RECONNECT_COUNT', 0)
          commit('CLEAR_ERROR')
          
          // 启动心跳
          dispatch('startHeartbeat')
          
          resolve(ws)
        }
        
        ws.onmessage = (event) => {
          try {
            const data = JSON.parse(event.data)
            console.log('WebSocket收到消息:', data)
            
            // 根据消息中的topic分发数据
            if (data.topic) {
              commit('SET_MESSAGE_DATA', { topic: data.topic, data: data })
            }
          } catch (error) {
            console.error('解析WebSocket消息失败:', error)
          }
        }
        
        ws.onclose = (event) => {
          console.log('WebSocket连接关闭:', event)
          commit('SET_CONNECTION_STATUS', false)
          dispatch('stopHeartbeat')
          
          // 如果不是主动关闭，尝试重连
          if (event.code !== 1000 && state.reconnectCount < state.maxReconnectCount) {
            dispatch('reconnect')
          }
        }
        
        ws.onerror = (error) => {
          console.error('WebSocket连接错误:', error)
          commit('SET_ERROR', error)
          reject(error)
        }
        
      } catch (error) {
        console.error('创建WebSocket连接失败:', error)
        commit('SET_ERROR', error)
        reject(error)
      }
    })
  },
  
  // 重连WebSocket
  reconnect({ commit, dispatch, state }) {
    if (state.reconnectCount >= state.maxReconnectCount) {
      console.error('WebSocket重连次数已达上限')
      return
    }
    
    commit('SET_RECONNECT_COUNT', state.reconnectCount + 1)
    console.log(`WebSocket重连中... (${state.reconnectCount}/${state.maxReconnectCount})`)
    
    setTimeout(() => {
      dispatch('initWebSocket')
    }, state.reconnectInterval)
  },
  
  // 关闭WebSocket连接
  closeWebSocket({ commit, dispatch, state }) {
    if (state.ws) {
      dispatch('stopHeartbeat')
      state.ws.close(1000, '主动关闭')
      commit('SET_WEBSOCKET', null)
      commit('SET_CONNECTION_STATUS', false)
      commit('SET_CURRENT_TOPIC', null)
    }
  },

  // 订阅topic
  subscribeTopic({ commit, dispatch, state }, { topic, params = {} }) {
    if (!state.ws || !state.isConnected) {
      console.error('WebSocket未连接，无法订阅topic:', topic)
      return Promise.reject(new Error('WebSocket未连接'))
    }

    // 如果当前已有订阅的topic，先取消订阅
    if (state.currentTopic && state.currentTopic !== topic) {
      dispatch('unsubscribeTopic', state.currentTopic)
    }

    const message = {
      topic,
      action: 'subscribe',
      params
    }

    try {
      state.ws.send(JSON.stringify(message))
      commit('SET_CURRENT_TOPIC', topic)
      console.log('订阅topic成功:', topic)
      return Promise.resolve()
    } catch (error) {
      console.error('订阅topic失败:', error)
      return Promise.reject(error)
    }
  },

  // 取消订阅topic
  unsubscribeTopic({ commit, state }, topic) {
    if (!state.ws || !state.isConnected) {
      console.error('WebSocket未连接，无法取消订阅topic:', topic)
      return
    }

    const message = {
      topic,
      action: 'unsubscribe'
    }

    try {
      state.ws.send(JSON.stringify(message))
      if (state.currentTopic === topic) {
        commit('SET_CURRENT_TOPIC', null)
      }
      // 清除该topic的消息数据
      commit('CLEAR_MESSAGE_DATA', topic)
      console.log('取消订阅topic成功:', topic)
    } catch (error) {
      console.error('取消订阅topic失败:', error)
    }
  },

  // 查询数据
  queryData({ state }, { topic, params = {} }) {
    if (!state.ws || !state.isConnected) {
      console.error('WebSocket未连接，无法查询数据')
      return Promise.reject(new Error('WebSocket未连接'))
    }

    const message = {
      topic,
      action: 'query',
      params
    }

    try {
      state.ws.send(JSON.stringify(message))
      console.log('查询数据请求发送成功:', topic)
      return Promise.resolve()
    } catch (error) {
      console.error('查询数据失败:', error)
      return Promise.reject(error)
    }
  },

  // 启动心跳
  startHeartbeat({ commit, dispatch, state }) {
    if (state.heartbeatTimer) {
      clearInterval(state.heartbeatTimer)
    }

    const timer = setInterval(() => {
      if (state.ws && state.isConnected) {
        try {
          state.ws.send(JSON.stringify({ action: 'ping' }))
        } catch (error) {
          console.error('发送心跳失败:', error)
          dispatch('reconnect')
        }
      }
    }, state.heartbeatInterval)

    commit('SET_HEARTBEAT_TIMER', timer)
  },

  // 停止心跳
  stopHeartbeat({ commit, state }) {
    if (state.heartbeatTimer) {
      clearInterval(state.heartbeatTimer)
      commit('SET_HEARTBEAT_TIMER', null)
    }
  }
}

const getters = {
  // 获取连接状态
  isConnected: state => state.isConnected,

  // 获取当前订阅的topic
  currentTopic: state => state.currentTopic,

  // 获取指定topic的消息数据
  getMessageData: state => topic => state.messageData[topic],

  // 获取所有消息数据
  getAllMessageData: state => state.messageData,

  // 获取错误信息
  getError: state => state.error,

  // 获取重连状态
  getReconnectInfo: state => ({
    count: state.reconnectCount,
    maxCount: state.maxReconnectCount,
    isReconnecting: state.reconnectCount > 0 && state.reconnectCount < state.maxReconnectCount
  })
}

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters
}
