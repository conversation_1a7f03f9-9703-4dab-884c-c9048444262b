<script setup>
import { computed } from 'vue'

const props = defineProps({
  theme: {
    type: Object,
    required: true
  },
  data: {
    type: Array,
    required: true
  },
})  

const { circleStyle, textStyle } = props.theme

const processedData = computed(() => {
  const elements = []
  props.data.forEach((item, itemIndex) => {
    if(item.circleConfig) {
      item.circleConfig.forEach((circle, circleIndex) => {
        elements.push({
          id: `depotWashLine-circle-${itemIndex}-${circleIndex}`,
          type: 'circle',   
          ...circle
        })
      })
    }

    if(item.textConfig) {
      item.textConfig.forEach((text, textIndex) => {
        elements.push({
          id: `depotWashLine-text-${itemIndex}-${textIndex}`,
          type: 'text',
          ...text
        })
      })
    }
  })
  return elements
})

const getCircleStyle = (element) => {
  // 如果没有stroke，则用fill,如果stroke和fill都没有，则不修改颜色
  const { cClrFill, cClrStroke } = element
  if(cClrFill && cClrStroke) {
    return {
      fill: `rgb(${cClrFill})`,
      stroke: `rgb(${cClrStroke})`
    }
  } else if(cClrFill) {
    return {
      fill: `rgb(${cClrFill})`,
      stroke: `rgb(${cClrFill})`
    }
  } else {
    return {
      
    }
  }
}
</script>

<template>
  <g attr="洗车线">
    <template v-for="element in processedData" :key="element.id">
      <circle v-if="element.type === 'circle'" :class="circleStyle" :style="getCircleStyle(element)" :cx="element.x" :cy="element.y" />
      <text v-else-if="element.type === 'text'" :class="textStyle" :x="element.x" :y="element.y">
        {{ element.text }}
      </text>
    </template>
  </g>
</template>

<style scoped></style>