# CBI系统Vuex状态管理使用指南

## 概述

本项目使用Vuex进行状态管理，主要目的是实现以下功能：
- 记录用户在各个模块的选中项目、搜索条件等
- 实现页面刷新后状态保持
- 跨组件状态共享
- 统一的状态管理模式

## 模块划分

### 1. driveGat模块
管理驱动网关页面的状态，包括：
- 时间输入值（timeValue, dateValue等）
- 设备选择状态
- 搜索条件
- 页面配置选项

### 2. historyPlayback模块
管理历史回放页面的状态，包括：
- 回放控制状态（播放/暂停、进度、速度）
- 回放目标选择
- 搜索和筛选条件
- 用户偏好设置

## 使用方法

### 在组件中使用Vuex

```javascript
import { useStore } from 'vuex'
import { computed, onMounted } from 'vue'

export default {
  setup() {
    const store = useStore()
    
    // 1. 获取状态
    const replayTarget = computed(() => store.state.historyPlayback.replayTarget)
    
    // 2. 使用getter
    const playbackSummary = computed(() => store.getters['historyPlayback/playbackSummary'])
    
    // 3. 提交mutation
    const updateTarget = (target) => {
      store.commit('historyPlayback/SET_REPLAY_TARGET', target)
    }
    
    // 4. 分发action
    const saveTarget = (target) => {
      store.dispatch('historyPlayback/setReplayTarget', target)
    }
    
    // 5. 页面挂载时恢复状态
    onMounted(() => {
      store.dispatch('historyPlayback/restoreFromStorage')
    })
    
    return {
      replayTarget,
      playbackSummary,
      updateTarget,
      saveTarget
    }
  }
}
```

### 双向绑定状态

```javascript
// 使用computed的get/set实现双向绑定
const searchForm = computed({
  get: () => store.state.historyPlayback.searchConditions,
  set: (value) => store.commit('historyPlayback/SET_SEARCH_CONDITIONS', value)
})
```

### 状态持久化

```javascript
import { saveToStorage, loadFromStorage } from '@/utils/storage'

// 保存状态
saveToStorage('historyPlayback_replayTarget', 'station')

// 读取状态
const savedTarget = loadFromStorage('historyPlayback_replayTarget', 'station')
```

## 状态持久化策略

### 自动保存
- 重要状态改变时自动保存到localStorage
- 使用防抖机制避免频繁保存
- 页面刷新时自动恢复状态

### 手动保存
- 提供手动保存和恢复方法
- 支持清空所有状态数据
- 支持导出/导入状态配置

## 开发规范

### 1. 命名规范
- State：使用驼峰命名，如 `replayTarget`
- Mutation：使用大写+下划线，如 `SET_REPLAY_TARGET`
- Action：使用驼峰命名，如 `setReplayTarget`
- Getter：使用驼峰命名，如 `playbackSummary`

### 2. 模块结构
```
store/
├── index.js          # 主store配置
├── modules/
│   ├── driveGat.js   # 驱动网关模块
│   └── historyPlayback.js # 历史回放模块
```

### 3. 状态设计原则
- 保持状态结构扁平化
- 避免冗余数据
- 合理使用getters计算派生状态
- 异步操作使用actions

## 调试和监控

### 1. 开发工具
- 使用Vue DevTools查看状态变化
- 在控制台查看状态保存/恢复日志

### 2. 日志输出
```javascript
console.log('状态已保存:', key, value)
console.log('状态已恢复:', key, parsed)
```

### 3. 错误处理
- 状态序列化/反序列化错误处理
- localStorage不可用时的降级方案

## 扩展指南

### 添加新模块
1. 在 `store/modules/` 目录创建新模块文件
2. 在 `store/index.js` 中导入并注册模块
3. 定义模块的state、mutations、actions、getters
4. 添加状态持久化逻辑

### 添加新状态
1. 在对应模块的state中定义新状态
2. 添加相应的mutations和actions
3. 更新storage.js中的存储键名
4. 在组件中使用新状态

## 注意事项

1. **性能优化**
   - 避免在computed中直接访问整个store
   - 合理使用getters缓存计算结果
   - 使用防抖延迟保存频繁变化的状态

2. **数据安全**
   - 敏感数据不要存储在localStorage
   - 注意状态数据的大小限制
   - 定期清理过期的状态数据

3. **兼容性**
   - 检查localStorage的可用性
   - 提供状态迁移机制
   - 向后兼容旧版本的状态结构

## 常见问题

### Q: 页面刷新后状态丢失？
A: 确保在组件挂载时调用了 `restoreFromStorage` action

### Q: 状态保存失败？
A: 检查localStorage是否可用，数据是否可序列化

### Q: 状态恢复异常？
A: 检查存储的数据格式是否正确，添加容错处理

### Q: 多标签页状态同步？
A: 可以监听storage事件实现跨标签页状态同步 
 