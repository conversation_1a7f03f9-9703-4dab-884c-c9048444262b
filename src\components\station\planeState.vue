<script setup>
import { computed } from 'vue'

const props = defineProps({
  theme: {
    type: Object,
    required: true
  },
  data: {
    type: Array,
    required: true
  },
})

const { circleStyle, textStyle } = props.theme

const getCircleStyle = (circle) => {
  const styles = {}
  if(circle.cClrFill) {
    styles.fill = `rgb(${circle.cClrFill})`
  }
  if(circle.cClrStroke) {
    styles.stroke = `rgb(${circle.cClrStroke})`
  }
  return styles
}
</script>

<template>
  <g :attr="'平流'">
    <template v-for="element in data" :key="element.usIndex+'planeState'">
      <circle v-for="(circle, circleIndex) in element.circleConfig" :key="circleIndex+'circle'" :class="circleStyle" :style="getCircleStyle(circle)" :cx="circle.x" :cy="circle.y" />
      <text v-for="(text, textIndex) in element.textConfig" :key="textIndex" :class="textStyle" :x="text.x" :y="text.y">
        {{ text.text }}
      </text>
    </template>
  </g>
</template>

<style scoped></style>