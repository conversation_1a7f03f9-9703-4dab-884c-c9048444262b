/**
 * 路由与Topic映射工具
 * 根据menu.js配置建立路由路径与WebSocket topic的映射关系
 */

import { menuData } from '../config/menu.js'

/**
 * 构建路由与topic的映射关系
 * @returns {Object} 路由路径到topic的映射对象
 */
export function buildRouteTopicMap() {
  const routeTopicMap = {}
  
  // 递归处理菜单数据
  function processMenuItem(item) {
    if (item.path && item.topic) {
      routeTopicMap[item.path] = item.topic
    }
    
    // 处理子菜单
    if (item.children && item.children.length > 0) {
      item.children.forEach(child => {
        processMenuItem(child)
      })
    }
  }
  
  // 处理所有菜单项
  menuData.forEach(item => {
    processMenuItem(item)
  })
  
  return routeTopicMap
}

/**
 * 根据路由路径获取对应的topic
 * @param {string} routePath 路由路径
 * @returns {string|null} 对应的topic，如果没有找到返回null
 */
export function getTopicByRoute(routePath) {
  const routeTopicMap = buildRouteTopicMap()
  return routeTopicMap[routePath] || null
}

/**
 * 根据topic获取对应的路由路径
 * @param {string} topic WebSocket topic
 * @returns {string|null} 对应的路由路径，如果没有找到返回null
 */
export function getRouteByTopic(topic) {
  const routeTopicMap = buildRouteTopicMap()
  
  for (const [route, topicValue] of Object.entries(routeTopicMap)) {
    if (topicValue === topic) {
      return route
    }
  }
  
  return null
}

/**
 * 获取菜单项的完整信息（包括父菜单信息）
 * @param {string} routePath 路由路径
 * @returns {Object|null} 菜单项信息，包括父菜单
 */
export function getMenuItemByRoute(routePath) {
  let result = null
  
  function findMenuItem(items, parentItem = null) {
    for (const item of items) {
      if (item.path === routePath) {
        result = {
          ...item,
          parent: parentItem
        }
        return true
      }
      
      if (item.children && item.children.length > 0) {
        if (findMenuItem(item.children, item)) {
          return true
        }
      }
    }
    return false
  }
  
  findMenuItem(menuData)
  return result
}

/**
 * 获取默认的topic（通常是首页对应的topic）
 * @returns {string|null} 默认topic
 */
export function getDefaultTopic() {
  // 查找第一个有topic的菜单项
  for (const item of menuData) {
    if (item.topic) {
      return item.topic
    }
    
    if (item.children && item.children.length > 0) {
      for (const child of item.children) {
        if (child.topic) {
          return child.topic
        }
      }
    }
  }
  
  return null
}

/**
 * 验证topic是否存在于菜单配置中
 * @param {string} topic WebSocket topic
 * @returns {boolean} topic是否有效
 */
export function isValidTopic(topic) {
  const routeTopicMap = buildRouteTopicMap()
  return Object.values(routeTopicMap).includes(topic)
}

/**
 * 获取所有可用的topics
 * @returns {Array} 所有topic的数组
 */
export function getAllTopics() {
  const routeTopicMap = buildRouteTopicMap()
  return [...new Set(Object.values(routeTopicMap))]
}

/**
 * 打印路由与topic的映射关系（调试用）
 */
export function debugRouteTopicMap() {
  const routeTopicMap = buildRouteTopicMap()
  console.table(routeTopicMap)
  return routeTopicMap
}
