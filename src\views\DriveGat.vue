<template>
  <div class="drive-gat-container">
    <!-- WebSocket数据监控面板 -->
    <div class="websocket-panel">
      <h3>驱动采集 - WebSocket数据监控</h3>
      <div class="status-info">
        <span>连接状态: </span>
        <span :class="connectionStatusClass">{{ connectionStatusText }}</span>
        <span> | 当前Topic: {{ currentTopic || '未订阅' }}</span>
      </div>

      <div v-if="websocketData" class="data-display">
        <h4>实时驱动数据:</h4>
        <div class="data-content">
          <pre>{{ JSON.stringify(websocketData, null, 2) }}</pre>
        </div>
      </div>

      <div class="actions">
        <button @click="queryDriverData" :disabled="!isConnected">
          查询驱动数据
        </button>
        <button @click="refreshData">刷新数据</button>
      </div>
    </div>

    <h2>时间输入组件演示</h2>
    
    <!-- 时间格式输入 -->
    <div class="demo-section">
      <h3>1. 时间格式 (HH:mm:ss)</h3>
      <TimeInput
        v-model="timeValue"
        format="time"
        label="请选择时间:"
        :show-current-time="true"
        @change="onTimeChange"
      />
      <p>当前时间值: {{ timeValue }}</p>
    </div>

    <!-- 日期格式输入 -->
    <div class="demo-section">
      <h3>2. 日期格式 (YYYY-MM-DD)</h3>
      <TimeInput
        v-model="dateValue"
        format="date"
        label="请选择日期:"
        :show-current-time="true"
        @change="onDateChange"
      />
      <p>当前日期值: {{ dateValue }}</p>
    </div>

    <!-- 禁用状态演示 -->
    <div class="demo-section">
      <h3>3. 禁用状态</h3>
      <TimeInput
        v-model="disabledValue"
        format="time"
        label="禁用的时间输入:"
        :disabled="true"
        :show-current-time="true"
      />
    </div>

    <!-- 无箭头演示 -->
    <div class="demo-section">
      <h3>4. 无箭头模式</h3>
      <TimeInput
        v-model="noArrowValue"
        format="date"
        label="无箭头的日期输入:"
        :show-arrows="false"
        :show-current-time="false"
        custom-placeholder="请输入日期"
      />
    </div>

    <!-- 调试模式演示 -->
    <div class="demo-section">
      <h3>5. 调试模式</h3>
      <TimeInput
        v-model="debugValue"
        format="time"
        label="调试模式:"
        :debug="true"
        :show-current-time="true"
      />
    </div>

    <!-- 手动控制演示 -->
    <div class="demo-section">
      <h3>6. 手动控制</h3>
      <TimeInput
        ref="manualTimeRef"
        v-model="manualValue"
        format="time"
        label="手动控制的时间输入:"
        :show-current-time="false"
      />
      <div style="margin-top: 10px;">
        <button @click="setCurrentTime">设置当前时间</button>
        <button @click="clearTime">清空</button>
        <button @click="focusInput">聚焦</button>
        <button @click="setSpecificTime">设置为 15:30:45</button>
      </div>
      <p>手动控制值: {{ manualValue }}</p>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, inject } from 'vue';
import { useStore } from 'vuex';
import TimeInput from '@/components/TimeInput.vue';

const store = useStore()

// 注入App.vue提供的数据和方法
const websocketData = inject('websocketData')
const queryData = inject('queryData')

// WebSocket相关计算属性
const isConnected = computed(() => store.getters['websocket/isConnected'])
const currentTopic = computed(() => store.getters['websocket/currentTopic'])

const connectionStatusClass = computed(() => {
    return isConnected.value ? 'connected' : 'disconnected'
})

const connectionStatusText = computed(() => {
    return isConnected.value ? '已连接' : '已断开'
})

// 监听WebSocket数据变化
watch(websocketData, (newData) => {
    if (newData) {
        console.log('DriveGat组件收到WebSocket数据:', newData)
        handleDriverData(newData)
    }
}, { deep: true })

// 处理驱动数据
const handleDriverData = (data) => {
    if (data.topic && data.topic.includes('driver')) {
        console.log('处理驱动相关数据:', data)
        // 这里可以更新驱动状态、IO状态等
    }
}

// 查询驱动数据
const queryDriverData = async () => {
    if (queryData && currentTopic.value) {
        try {
            await queryData(currentTopic.value, {
                filter: 'driver_status',
                timestamp: Date.now()
            })
            console.log('查询驱动数据请求已发送')
        } catch (error) {
            console.error('查询驱动数据失败:', error)
        }
    }
}

// 刷新数据
const refreshData = () => {
    console.log('刷新驱动数据')
    queryDriverData()
}

// 响应式数据
const timeValue = ref('');
const dateValue = ref('');
const disabledValue = ref('');
const noArrowValue = ref('');
const debugValue = ref('');
const manualValue = ref('');

// 组件引用
const manualTimeRef = ref(null);

// 事件处理函数
const onTimeChange = (value) => {
  console.log('时间变化:', value);
};

const onDateChange = (value) => {
  console.log('日期变化:', value);
};

// 手动控制函数
const setCurrentTime = () => {
  const now = new Date();
  const timeString = `${String(now.getHours()).padStart(2, '0')}:${String(now.getMinutes()).padStart(2, '0')}:${String(now.getSeconds()).padStart(2, '0')}`;
  manualTimeRef.value?.setValue(timeString);
};

const clearTime = () => {
  manualTimeRef.value?.setValue('');
};

const focusInput = () => {
  manualTimeRef.value?.focus();
};

const setSpecificTime = () => {
  manualTimeRef.value?.setValue('15:30:45');
};

</script>

<style scoped>
.drive-gat-container {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
  font-family: Arial, sans-serif;
  overflow: scroll;
  height: 90vh;
}

/* WebSocket面板样式 */
.websocket-panel {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 16px;
  border-radius: 8px;
  margin-bottom: 20px;
}

.websocket-panel h3 {
  margin: 0 0 12px 0;
  color: white;
}

.status-info {
  margin-bottom: 12px;
  font-size: 14px;
}

.status-info .connected {
  color: #4CAF50;
  font-weight: bold;
}

.status-info .disconnected {
  color: #f44336;
  font-weight: bold;
}

.data-display {
  margin: 12px 0;
}

.data-display h4 {
  margin: 0 0 8px 0;
  color: white;
}

.data-content {
  background: rgba(0, 0, 0, 0.3);
  padding: 12px;
  border-radius: 4px;
  max-height: 200px;
  overflow-y: auto;
}

.data-content pre {
  margin: 0;
  font-size: 12px;
  white-space: pre-wrap;
  word-break: break-all;
}

.websocket-panel .actions {
  display: flex;
  gap: 8px;
}

.websocket-panel .actions button {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.3);
  padding: 6px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
}

.websocket-panel .actions button:hover:not(:disabled) {
  background: rgba(255, 255, 255, 0.3);
}

.websocket-panel .actions button:disabled {
  background: rgba(255, 255, 255, 0.1);
  cursor: not-allowed;
}

h2 {
  color: #333;
  border-bottom: 2px solid #409eff;
  padding-bottom: 10px;
  margin-bottom: 30px;
}

h3 {
  color: #666;
  margin-top: 30px;
  margin-bottom: 15px;
}

.demo-section {
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  background: #fafafa;
}

.demo-section p {
  margin-top: 10px;
  color: #666;
  font-size: 14px;
}

button {
  padding: 8px 16px;
  margin-right: 10px;
  margin-bottom: 5px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  background: #fff;
  cursor: pointer;
  transition: all 0.2s;
}

button:hover {
  background: #ecf5ff;
  border-color: #b3d8ff;
  color: #409eff;
}

button:active {
  background: #409eff;
  border-color: #409eff;
  color: #fff;
}
</style>