<template>
    <div class="station-container">
        <!-- WebSocket数据显示区域 -->
        <!-- <div class="websocket-data-panel">
            <h3>WebSocket数据监控</h3>
            <div class="data-info">
                <p><strong>当前Topic:</strong> {{ currentTopic || '未订阅' }}</p>
                <p><strong>连接状态:</strong>
                    <span :class="connectionStatusClass">{{ connectionStatusText }}</span>
                </p>
                <p><strong>最后更新:</strong> {{ lastUpdateTime || '无数据' }}</p>
            </div>

            <div class="realtime-data" v-if="websocketData">
                <h4>实时数据:</h4>
                <pre>{{ JSON.stringify(websocketData, null, 2) }}</pre>
            </div>

            <div class="actions">
                <button @click="queryStationData" :disabled="!isConnected">
                    查询站场数据
                </button>
                <button @click="clearData">清除数据</button>
            </div>
        </div> -->

        <!-- 原有的Line组件 -->
        <div class="station-content">
            <Line />
        </div>
    </div>
</template>

<script setup>
import { ref, computed, watch, inject, onMounted, onBeforeUnmount } from 'vue';
import { useStore } from 'vuex';
import Line from '../components/station/line.vue';

const store = useStore()

// 注入App.vue提供的数据和方法
const websocketData = inject('websocketData')
const queryData = inject('queryData')

// 本地状态
const lastUpdateTime = ref(null)

// 计算属性
const isConnected = computed(() => store.getters['websocket/isConnected'])
const currentTopic = computed(() => store.getters['websocket/currentTopic'])

const connectionStatusClass = computed(() => {
    return isConnected.value ? 'connected' : 'disconnected'
})

const connectionStatusText = computed(() => {
    return isConnected.value ? '已连接' : '已断开'
})

// 监听WebSocket数据变化
watch(websocketData, (newData) => {
    if (newData) {
        lastUpdateTime.value = new Date().toLocaleString()
        console.log('Station组件收到WebSocket数据:', newData)

        // 在这里可以处理具体的业务逻辑
        handleStationData(newData)
    }
}, { deep: true })

// 处理站场数据的业务逻辑
const handleStationData = (data) => {
    // 根据数据类型进行不同的处理
    if (data.topic && data.topic.includes('station')) {
        console.log('处理站场相关数据:', data)
        // 这里可以更新站场图的状态、信号灯状态等
    }
}

// 查询站场数据
const queryStationData = async () => {
    if (queryData && currentTopic.value) {
        try {
            await queryData(currentTopic.value, {
                filter: 'station_status',
                timestamp: Date.now()
            })
            console.log('查询站场数据请求已发送')
        } catch (error) {
            console.error('查询站场数据失败:', error)
        }
    }
}

// 清除数据
const clearData = () => {
    lastUpdateTime.value = null
    console.log('数据已清除')
}

onMounted(() => {
    console.log('Station组件已挂载')
})

onBeforeUnmount(() => {
    console.log('Station组件即将卸载')
})
</script>

<style lang="scss" scoped>
.station-container {
    width: 100%;
    height: 100vh;
    background-color: #000;
    color: #fff;
    position: relative;
    overflow: hidden;
    display: flex;
}

.websocket-data-panel {
    width: 350px;
    background-color: rgba(255, 255, 255, 0.1);
    padding: 16px;
    overflow-y: auto;
    border-right: 1px solid #333;

    h3, h4 {
        margin: 0 0 12px 0;
        color: #4CAF50;
    }

    .data-info {
        margin-bottom: 16px;

        p {
            margin: 8px 0;
            font-size: 14px;

            strong {
                color: #ccc;
            }
        }

        .connected {
            color: #4CAF50;
        }

        .disconnected {
            color: #f44336;
        }
    }

    .realtime-data {
        margin-bottom: 16px;

        pre {
            background-color: rgba(0, 0, 0, 0.5);
            padding: 12px;
            border-radius: 4px;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
            white-space: pre-wrap;
            word-break: break-all;
        }
    }

    .actions {
        display: flex;
        gap: 8px;
        flex-wrap: wrap;

        button {
            background-color: #2196F3;
            color: white;
            border: none;
            padding: 8px 12px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;

            &:hover:not(:disabled) {
                background-color: #1976D2;
            }

            &:disabled {
                background-color: #666;
                cursor: not-allowed;
            }
        }
    }
}

.station-content {
    flex: 1;
    position: relative;
    overflow: hidden;
}
</style>