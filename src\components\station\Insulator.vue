<template>
  <g attr="绝缘节">
    <template v-for="item in processedData" :key="item.usIndex+'insulator'">
      <line :class="lineStyle" :x1="item.x1" :y1="item.y1" :x2="item.x2" :y2="item.y2" />
      <circle v-if="item.circleX>0" :class="circleStyle" :cx="item.circleX" :cy="item.circleY" />
    </template>
  </g>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
    theme: {
        type: Object,
        required: true
    },
    data: {
        type: Array,
        required: true
    }
})

const { lineStyle, circleStyle } = props.theme

// 使用 computed 缓存处理后的数据，避免重复计算
const processedData = computed(() => {
    return props.data.map(item => {
        const setObj = item.lineConfig?.[0]
        const circleObj = item.circleConfig?.[0]
        return {
            ...item, // 保留原始数据
            x1: setObj?.x1,
            y1: setObj?.y1,
            x2: setObj?.x2,
            y2: setObj?.y2,
            circleX: circleObj?.x,
            circleY: circleObj?.y
        }
    })
})
</script>

<style lang="scss" scoped></style>