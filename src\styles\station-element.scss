.line-style-1 {
    stroke: rgb(85,120,182);
    stroke-width: 2;
}

.line-style-2 {
    stroke: rgb(128,128,128);
    stroke-width: 2;
}

.line-style-3 {
    stroke: rgb(128,128,128);
    stroke-width: 1;
}

.line-style-4 {
    stroke: rgb(128,128,128);
    stroke-width: 3;
}       

.line-style-5 {
    stroke-width: 3;
    stroke: rgb(128, 128, 128);
}

.line-container.flash-on .line-style-5.flash-active {
    stroke: rgb(255, 0, 0);
}

.line-style-6 {
    stroke: rgb(128,128,128);
    stroke-width: 2;
}

.line-style-7 {
    stroke: rgb(188,67,180);
    stroke-width: 2;
}

.line-style-8 {
    stroke: rgb(0,255,0);
    stroke-width: 1;
}

.line-style-9 {
    stroke: rgb(0,0,0);
    stroke-width: 3;
}

.line-style-10 {
    stroke: rgb(85,120,182);
    stroke-width: 1;
}

.line-style-11 {
    stroke: rgb(128,128,128);
    stroke-width: 3;
}

.line-style-12 {
    stroke: rgb(0,128,0);
    stroke-width: 1;
}

.line-style-13 {
    stroke: rgb(255,255,255);
    stroke-width: 1;
}


.circle-style-1 {
    stroke-width: 1;
    r: 7;
    fill: rgb(0, 0, 0);
    stroke: rgb(85,120,182);
}

.line-container.flash-on .circle-style-1.flash-active {
    fill: rgb(255,0,0);
    stroke: rgb(255,0,0);
}

.circle-style-2 {
    stroke-width: 1;
    stroke: rgb(85,120,182);
    fill: rgb(0,0,0);
    r: 7;
}
.circle-style-3 {
    stroke-width: 1;
    fill: none;
    stroke: rgb(255,255,255);
    r: 7;
}
.circle-style-4 {
    stroke-width: 1;
    fill: rgb(255,0,0);
    stroke: rgb(255,255,255);
    r: 7;
}
.circle-style-5 {
    stroke-width: 1;
    stroke: rgb(85,120,182);
    fill: rgb(0,0,0);
    r: 7;
    // 闪烁颜色 黑色
}
.line-container.flash-on .circle-style-5.flash-active {
    fill: rgb(60,66,235);
    stroke: rgb(60,66,235);
}
.circle-style-6 {
    stroke: rgb(255,0,0);
    stroke-width: 2;
    fill: none;
    r: 7;
}
.circle-style-7 {
    stroke: rgb(255,0,0);
    stroke-width: 1;
    fill: none;
    r: 7;
}

.text-style-1 {
    font-size: 10px;
    fill: rgb(192,192,192);
    dominant-baseline: hanging;
}

.text-style-2 {
    font-size: 10px;
    fill: rgb(255,0,0);
    dominant-baseline: hanging;
}

.text-style-3 {
    font-size: 10px;
    fill: rgb(85,120,182);
    dominant-baseline: hanging;
}

.text-style-4 {
    font-size: 9px;
    fill: rgb(0,0,0);
    dominant-baseline: ideographic;
}

.text-style-5 {
    font-size: 9px;
    fill: rgb(255,0,0);
}

.text-style-6 {
    font-size: 10px;
    fill: rgb(255,255,0);
    dominant-baseline: hanging;
}

.text-style-7 {
    font-size: 10px;
    fill: rgb(255,255,255);
    dominant-baseline: hanging;
}

.text-style-8 {
    font-size: 10px;
    fill: rgb(0,0,0);
    dominant-baseline: hanging;
}

.text-style-9 {
    font-size: 10px;
    fill: rgb(255,255,0);
    dominant-baseline: hanging;
}

.text-style-10 {
    font-size: 9px;
    fill: rgb(255,255,255);
    dominant-baseline: hanging;
}

.text-style-11 {
    font-size: 9px;
    fill: rgb(192,192,192);
    dominant-baseline: hanging;
}


.arrow-style-1 {
    stroke: rgb(239,239,239);
    stroke-width: 1;
    fill: rgb(0,0,0)
}

// 矩形框样式
.rect-style-1 {
    stroke: rgb(239,239,239);
    stroke-width: 1;
    fill: rgb(239,239,239)
}

.rect-style-2 {
    stroke: rgb(255,255,0);
    stroke-width: 1;
    fill: rgb(255,255,0)
}

.rect-style-3 {
    stroke: rgb(128,128,128);
    stroke-width: 1;
    fill: rgb(0,128,0)
}

.line-container.flash-on .rect-style-3.flash-active {
    fill: rgb(0,0,0);
}

.rect-style-4 {
    stroke: rgb(128,128,128);
    stroke-width: 1;
    fill: rgb(80,80,255)
}

.line-container.flash-on .rect-style-4.flash-active {
    fill: rgb(0,0,0);
}

.rect-style-5 {
    stroke: rgb(128,128,128);
    stroke-width: 1;
    fill: rgb(128,128,128)
}

.line-container.flash-on .rect-style-5.flash-active {
    fill: rgb(0,0,0);
}

.rect-style-6 {
    stroke: rgb(255,0,0);
    stroke-width: 1;
    fill: none;
}

.rect-style-7 {
    stroke: rgb(128,128,128);
    stroke-width: 1;
    fill: rgb(0,128,0);
}

.rect-style-8 {
    stroke: rgb(128,128,128);
    stroke-width: 1;
    fill: rgb(80,80,255);
}

.rect-style-9 {
    stroke: rgb(128,128,128);
    stroke-width: 1;
    fill: rgb(128,128,128);
}

//交叉线样式
.cross-line-style-1 {
    stroke: rgb(85,120,182);
    stroke-width: 1;
    fill: rgb(85,120,182);
}


