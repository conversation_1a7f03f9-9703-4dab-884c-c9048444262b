<template>
  <g attr="表示灯">
    <template v-for="element in processedData" :key="element.id+'indicator'">
      <circle v-if="element.type === 'circle'" :class="getClass(element)" :style="getCircleStyle(element)" :cx="element.x" :cy="element.y" />
      <text v-else-if="element.type === 'text'" :class="textStyle" :x="element.x" :y="element.y">
        {{ element.text }}
      </text>
    </template>
  </g>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  data: {
    type: Array,
    required: true
  },
  theme: {
    type: Object,
    required: true
  }
})

const { circleStyle, textStyle } = props.theme

const getCircleStyle = (element) => {
  const styles = {}
  if (element.cClrFill) {
    styles.fill = `rgb(${element.cClrFill})`
  }
  if (element.cClrStroke) {
    styles.stroke = `rgb(${element.cClrStroke})`
  }
  return styles
}

const processedData = computed(() => {
  if (!props.data) return [];
  const elements = []
  props.data.forEach((item, itemIndex) => {
    if(item.circleConfig) {
      item.circleConfig.forEach((circle, circleIndex) => {
        elements.push({
          id: `circle-${itemIndex}-${circleIndex}`,
          type: 'circle',
          ...circle
        })
      })
    }

    if(item.textConfig) {
      item.textConfig.forEach((text, textIndex) => {
        elements.push({
          id: `text-${itemIndex}-${textIndex}`,
          type: 'text',
          x: text.x,
          y: text.y,
          text: text.text,
        })
      })
    }
  })
  return elements
})

const getClass = (element) => {
  if(element.style) {
    return `circle-style-${element.style}`
  } else {
    return circleStyle
  }
}

</script>
<style scoped>

</style>
