import { createRouter, createWebHistory } from 'vue-router'
import HomeView from '../views/HomeView.vue'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'station',
      component: ()=>import('../views/Station.vue'),
    },
    {
      path: '/driver',
      name: 'driver',
      component: ()=>import('../views/DriveGat.vue'),
    },
    // {
    //   path: '/networkStatus',
    //   name: 'networkStatus',
    //   component: ()=>import('../views/NetworkStatus.vue'),
    // },
    // {
    //   path: '/safeInterface',
    //   name: 'safeInterface',
    //   component: ()=>import('../views/SafeInterface.vue'),
    // },
    // {
    //   path: '/alarmDiagnosis',
    //   name: 'alarmDiagnosis',
    //   component: ()=>import('../views/AlarmDiagnosis.vue'),
    // },
    // {
    //   path: '/infoQuery',
    //   name: 'infoQuery',
    //   component: ()=>import('../views/InfoQuery.vue'),
    // },
    {
      path: '/historyPlayback',
      name: 'historyPlayback',
      component: ()=>import('../views/HistoryPlayback.vue'),
    },
    {
      path: '/websocket-test',
      name: 'websocketTest',
      component: ()=>import('../views/WebSocketTest.vue'),
    },
    // {
    //   path: '/auxiliaryFunction',
    //   name: 'auxiliaryFunction',
    //   component: ()=>import('../views/AuxiliaryFunction.vue'),
    // },
  ],
})

export default router
