// 我自己的一些理解
// 这里是站场图绘制方案的顶层配置文件

import Singnal from "@/components/station/singnal.vue";

// 在这里指定站场图元中的基本图元使用哪套样式
export const stationThemeConfig = {
    default: {
        // 区段
        section: {
            lineStyle: 'line-style-5',
            textStyle: 'text-style-1',
        },
        // 道岔
        switch: {
            line1Style: 'line-style-5',
            line2Style: 'line-style-9',
            textStyle: 'text-style-2',
            redTextStyle: 'text-style-2',
            circleStyle: 'circle-style-6',
            redRectStyle: 'rect-style-6',
        },
        // 绝缘节
        insulator: {
            lineStyle: 'line-style-2',
            circleStyle: 'circle-style-7',
        },
        // 列车信号机
        singnal: {
            lineStyle: 'line-style-1',
            textStyle: 'text-style-3',
            redTextStyle: 'text-style-2',
            circleStyle: 'circle-style-1',
            circleStyle2: 'circle-style-2',
            crossingStyle: 'cross-line-style-1',
        },
        // 调车信号机
        dSignal: {
            lineStyle: 'line-style-1',
            circleStyle: 'circle-style-5',
            textStyle: 'text-style-1',
            redTextStyle: 'text-style-2',
        },
        text: {
            textStyle: 'text-style-1',
        },
        // 64DA
        SFDA: {
            arrowStyle: 'arrow-style-1',
            textStyle: 'text-style-1',
            redTextStyle: 'text-style-2',
            circleStyle: 'circle-style-3',
        },
        // 64DB
        SFDB: {
            arrowStyle: 'arrow-style-1',
            textStyle: 'text-style-1',
            redTextStyle: 'text-style-2',
        },
        stationButton: {
            rectStyle: 'rect-style-7',
            textStyle: 'text-style-4',
        },
        indicator: {
            circleStyle: 'circle-style-3',
            textStyle: 'text-style-1',
        },
        noRoute: {
            circleStyle: 'circle-style-3',
            textStyle: 'text-style-1',
            redTextStyle: 'text-style-2',
        },
        arriveHump: {
            lineStyle: 'line-style-1',
            arrowStyle: 'arrow-style-1',
            circleStyle1: 'circle-style-2',
            circleStyle2: 'circle-style-3',
            textStyle: 'text-style-1',
        },
        depotWashLine: {
            circleStyle: 'circle-style-3',
            textStyle: 'text-style-1',
        },
        planeState: {
            circleStyle: 'circle-style-3',
            textStyle: 'text-style-1',
        },
        signalIndicator: {
            circleStyle: 'circle-style-2',
            textStyle: 'text-style-1',
            lineStyle: 'line-style-1',
        },
        simpleHump: {
            circleStyle1: 'circle-style-2',
            circleStyle2: 'circle-style-3',
            textStyle: 'text-style-1',
            lineStyle: 'line-style-1',
        },
        autoBlock: {
            circleStyle: 'circle-style-3',
            textStyle: 'text-style-1',
            arrowStyle: 'arrow-style-1',
        },
        stationConcat: {
            circleStyle: 'circle-style-3',
            textStyle: 'text-style-1',
            lineStyle: 'line-style-1',
            arrowStyle: 'arrow-style-1',
        },
        box: {
            lineStyle: 'line-style-13',
        },
        ramp: {
            lineStyle: 'line-style-10',
            textStyle: 'text-style-3'
        },
        interlinkSwitch: {
            textStyle: 'text-style-1',
            arrowStyle: 'arrow-style-1',
            lineStyle: 'line-style-1',
            circleStyle: 'circle-style-1', 
            circleStyle2: 'circle-style-5', 
            circleStyle3: 'circle-style-3', 
        },
        endLine: {
            lineStyle: 'line-style-3',
        }
    },
    theme2: {
        // 待扩充
    }
}