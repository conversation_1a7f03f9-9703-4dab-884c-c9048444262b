<template>
  <g attr="到达驼峰">
    <template v-for="element in processedData" :key="element.id+'arriveHump'">
      <line v-if="element.type === 'line'" :class="lineStyle" :x1="element.x1" :y1="element.y1" :x2="element.x2" :y2="element.y2" />
      <circle v-if="element.type === 'sigCircle'" :class="getClass(element)"  :cx="element.x" :cy="element.y"  />
      <circle v-if="element.type === 'circle2'" :class="circleStyle2" :cx="element.x" :cy="element.y"  />
      <text v-if="element.type === 'text'" :class="textStyle" :x="element.x" :y="element.y">
        {{ element.text }}
      </text>
      <polygon v-if="element.type === 'arrow'" :class="arrowStyle" :points="getArrowPoints(element)" />
    </template>
  </g>
</template>

<script setup>
import { computed } from 'vue'  

const props = defineProps({
  theme: {
    type: Object,
    required: true
  },
  data: {
    type: Array,
    required: true
  },
})

const { lineStyle, arrowStyle, circleStyle1, circleStyle2, textStyle } = props.theme

const processedData = computed(() => {
    const elements = []
    props.data.forEach((item, itemIndex) => {
        if(item.lineConfig) {
            item.lineConfig.forEach((line, lineIndex) => {
                elements.push({
                    id: `ArriveHump-line-${itemIndex}-${lineIndex}`,
                    type: 'line',
                    x1: line.x1,
                    y1: line.y1,
                    x2: line.x2,
                    y2: line.y2,
                })
            })
        }

        if(item.arrowConfig) {
            item.arrowConfig.forEach((arrow, arrowIndex) => {
                elements.push({
                    id: `ArriveHump-arrow-${itemIndex}-${arrowIndex}`,
                    type: 'arrow',
                    x: arrow.x,
                    y: arrow.y,
                    dir: arrow.dir
                })
            })
        }

        if(item.sigCircleConfig) {
            item.sigCircleConfig.forEach((circle, circleIndex) => {
                elements.push({
                    id: `ArriveHump-sigCircle-${itemIndex}-${circleIndex}`,
                    type: 'sigCircle',
                    x: circle.x,
                    y: circle.y,
                })
            })
        }

        if(item.circleConfig) {
            item.circleConfig.forEach((circle, circleIndex) => {
                elements.push({
                    id: `ArriveHump-circle2-${itemIndex}-${circleIndex}`,
                    type: 'circle2',
                    x: circle.x,
                    y: circle.y,
                })
            })
        }

        if(item.textConfig) {
            item.textConfig.forEach((text, textIndex) => {
                elements.push({
                    id: `ArriveHump-text-${itemIndex}-${textIndex}`,
                    type: 'text',
                    x: text.x,
                    y: text.y,
                    text: text.text,
                })
            })
        }
    })
    return elements
})

// 使用 Map 缓存箭头点计算结果
const arrowPointsCache = new Map()

const getArrowPoints = (arrow) => {
  const { x, y, dir } = arrow;
  
  // 创建缓存键
  const cacheKey = `${x}-${y}-${dir}`
  
  // 检查缓存
  if (arrowPointsCache.has(cacheKey)) {
    return arrowPointsCache.get(cacheKey)
  }
  
  const points = [];
  // 1左，2右
  if(dir === 1){
    points.push(`${x},${y-3}`)
    points.push(`${x-12},${y-3}`)
    points.push(`${x-12},${y-7}`)
    points.push(`${x-19},${y}`)
    points.push(`${x-12},${y+7}`)
    points.push(`${x-12},${y+3}`)
    points.push(`${x},${y+3}`)
  } else if(dir === 2){
    points.push(`${x},${y-3}`)
    points.push(`${x+12},${y-3}`)
    points.push(`${x+12},${y-7}`)
    points.push(`${x+19},${y}`)
    points.push(`${x+12},${y+7}`)
    points.push(`${x+12},${y+3}`)
    points.push(`${x},${y+3}`)
  }
  
  const result = points.join(' ');
  // 缓存结果
  arrowPointsCache.set(cacheKey, result)
  
  return result;
}

const getClass = (element) => {
  if(element.style) {
    return `circle-style-${element.style}`
  } else {
    return circleStyle1
  }
}

</script>

<style scoped lang="scss">  

</style>