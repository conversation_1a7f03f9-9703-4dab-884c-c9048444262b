<template>
    <g v-for="(button, index) in processedButtons" :key="index+'stationButton'" :attr="index+'stationButton'">
        <template v-if="button.hasText&&button.rectWidth>=50">
            <rect :x="button.rect.x1" :y="button.rect.y1" :class="button.rect['realStyle']? button.rect['realStyle'] : rectStyle" :width="button.rectWidth" :height="button.rectHeight" />
            <polyline stroke-width="1" class="black-rect" :points="button.tblackPointArray" />
            <polyline stroke-width="1" class="light-gray-rect" :points="button.tlightGrayPointArray" />
            <polyline stroke-width="1" class="deep-gray-rect" :points="button.tdeepGrayPointArray" />
            <!-- 单行文本 -->
            <text 
                v-if="button.textLines.length <= 1"
                :x="button.text.x+25" 
                :y="button.text.y" 
                :class="button.text['realStyle']? button.text['realStyle'] : textStyle"
                text-anchor="middle"
            >
                {{ button.textLines[0] }}
            </text>
            <!-- 多行文本 -->
            <text 
                v-else
                :x="button.text.x+25" 
                :y="button.text.y" 
                :class="button.text['realStyle']? button.text['realStyle'] : textStyle"
                text-anchor="middle"
            >
                <tspan
                    v-for="(line, idx) in button.textLines"
                    :key="idx"
                    :x="button.text.x+25"
                    :y="button.text.y-6"
                    :dy="idx === 0 ? 0 : '1.2em'"
                    text-anchor="middle"
                >
                    {{ line }}
                </tspan>
            </text>
        </template>
        <template v-else>
            <rect :class="button.rect['realStyle']? button.rect['realStyle'] : rectStyle" :x="button.rect.x1" :y="button.rect.y1" rx="1" width="14" height="14" />
            <polyline stroke-width="1" class="black-rect" :points="button.blackPointArray" />
            <polyline stroke-width="1" class="light-gray-rect" :points="button.lightGrayPointArray" />
            <polyline stroke-width="1" class="deep-gray-rect" :points="button.deepGrayPointArray" />
            <text 
                v-if="button.hasText"
                :x="button.text.x" 
                :y="button.text.y" 
                :class="button.text['realStyle']? button.text['realStyle'] : textStyle"
            >
                {{ button.text.text }}
            </text>
        </template>
    </g>
</template>

<script setup>
import { computed, onMounted } from 'vue';

const props = defineProps({
    data: {
        type: Array,
        required: true
    },
    theme: {
        type: Object,
        required: true
    },
});

const { rectStyle, textStyle } = props.theme

// 文本分行处理函数
const splitText = (text) => {
    if (!text) return [""];
    const len = text.length;
    if (len === 5) {
        return [text.slice(0, 1), text.slice(1)];
    } else if (len === 4) {
        return [text.slice(0, 2), text.slice(2)];
    }
    return [text];
};

const processedButtons = computed(() => {
    if (!props.data) return [];
    
    return props.data.flatMap(item => {
        // 检查item中是否有文本配置
        const hasText = item.textConfig && item.textConfig.length > 0;

        // 如果没有rectConfig，则此item不生成任何按钮
        if (!item.rectConfig || item.rectConfig.length === 0) {
            return [];
        }

        // 遍历所有rect，并根据hasText标志生成按钮对象
        return item.rectConfig.map(rect => {
            const baseX = rect.x1;
            const baseY = rect.y1;
            const rectWidth = Math.abs(rect.x2 - rect.x1);
            const rectHeight = Math.abs(rect.y2 - rect.y1);
            if(rect.hasOwnProperty('style')) {
              rect['realStyle'] = `rect-style-${rect['style']}`
            }
            
            const textConfig = hasText ? item.textConfig[0] : null;
            if(textConfig&&textConfig.hasOwnProperty('style')) {
                textConfig['realStyle'] = `text-style-${textConfig['style']}`
            }

            return {
                usIndex: item.usIndex, 
                hasText: hasText,
                text: textConfig,
                textLines: hasText ? splitText(textConfig.text || "") : [],
                rectWidth: rectWidth,
                rectHeight: rectHeight,
                rect: rect, 
                // 仅在没有文本时才需要这些多边形来绘制3D效果
                blackPointArray: `${baseX + 13},${baseY + 0.5} ${baseX + 13},${baseY + 13} ${baseX + 0.5},${baseY + 13}`,
                lightGrayPointArray: `${baseX + 1},${baseY + 12.5} ${baseX + 1},${baseY + 1} ${baseX + 12.5},${baseY + 1}`,
                deepGrayPointArray: `${baseX + 12},${baseY + 1.5} ${baseX + 12},${baseY + 12} ${baseX + 1.5},${baseY + 12}`,
                tblackPointArray: `${baseX + 49},${baseY + 0.5} ${baseX + 49},${baseY + 31} ${baseX + 0.5},${baseY + 31}`,
                tlightGrayPointArray: `${baseX + 1},${baseY + 30.5} ${baseX + 1},${baseY + 1} ${baseX + 48.5},${baseY + 1}`,
                tdeepGrayPointArray: `${baseX + 48},${baseY + 1.5} ${baseX + 48},${baseY + 30} ${baseX + 1.5},${baseY + 30}`,
            };
        });
    });
});


</script>

<style scoped>
.black-rect {
    stroke: rgb(0, 0, 0);
    stroke-width: 1;
    fill: none;
}

.light-gray-rect {
    stroke: rgb(191, 191, 193);
    fill: none;
}

.deep-gray-rect {
    stroke: rgb(138, 138, 138);
    fill: none;
}
</style>